const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const logger = require('./utils/logger');
const config = require('./config');
const errorHandler = require('./middleware/errorHandler');
const rateLimiter = require('./middleware/rateLimiter');
const WebSocketService = require('./websocket');

// 导入路由
const apiRoutes = require('./routes/api');
const proxyRoutes = require('./routes/proxy');
const tampermonkeyRoutes = require('./routes/tampermonkey');

class WebProxyService {
  constructor() {
    this.apps = new Map(); // 存储多个端口的应用实例
    this.servers = new Map(); // 存储服务器实例
    this.wsService = new WebSocketService(); // WebSocket服务
    this.startTime = Date.now(); // 服务启动时间
  }

  // 创建Express应用
  createApp(portConfig) {
    const app = express();

    // 基础中间件
    app.use(helmet());
    app.use(cors(portConfig.cors || {}));
    app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true }));

    // 限流中间件
    app.use(rateLimiter);

    // 健康检查
    app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        port: portConfig.port,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        wsConnections: this.wsService.getStats().connectedClients
      });
    });

    // API路由
    app.use('/api', apiRoutes);
    app.use('/proxy', proxyRoutes);
    app.use('/tampermonkey', tampermonkeyRoutes);

    // 错误处理中间件
    app.use(errorHandler);

    return app;
  }

  // 启动指定端口的服务
  startServer(portConfig) {
    const app = this.createApp(portConfig);
    
    return new Promise((resolve, reject) => {
      const server = app.listen(portConfig.port, portConfig.host || '0.0.0.0', (err) => {
        if (err) {
          logger.error(`服务启动失败 - 端口 ${portConfig.port}:`, err);
          reject(err);
        } else {
          logger.info(`服务已启动 - 端口 ${portConfig.port}`);
          this.apps.set(portConfig.port, app);
          this.servers.set(portConfig.port, server);
          resolve(server);
        }
      });

      server.on('error', (err) => {
        logger.error(`服务器错误 - 端口 ${portConfig.port}:`, err);
      });
    });
  }

  // 启动所有配置的服务
  async startAllServers() {
    const promises = config.ports.map(portConfig => this.startServer(portConfig));

    try {
      await Promise.all(promises);

      // 启动WebSocket服务
      if (config.tampermonkey.enableWebSocket) {
        this.wsService.start(config.tampermonkey.wsPort);
      }

      logger.info('所有服务已成功启动');
    } catch (error) {
      logger.error('启动服务时发生错误:', error);
      process.exit(1);
    }
  }

  // 优雅关闭
  async shutdown() {
    logger.info('开始关闭服务...');

    // 关闭WebSocket服务
    this.wsService.close();

    const shutdownPromises = Array.from(this.servers.values()).map(server => {
      return new Promise((resolve) => {
        server.close(() => {
          resolve();
        });
      });
    });

    await Promise.all(shutdownPromises);
    logger.info('所有服务已关闭');
    process.exit(0);
  }
}

// 创建服务实例
const webProxyService = new WebProxyService();

// 启动服务
webProxyService.startAllServers();

// 优雅关闭处理
process.on('SIGTERM', () => webProxyService.shutdown());
process.on('SIGINT', () => webProxyService.shutdown());

module.exports = webProxyService;
