// 最简单的测试服务器
const http = require('http');

console.log('正在启动简单测试服务器...');

const server = http.createServer((req, res) => {
  console.log(`收到请求: ${req.method} ${req.url}`);
  
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-TM-Script');
  res.setHeader('Content-Type', 'application/json');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 路由处理
  if (req.url === '/health' && req.method === 'GET') {
    const response = {
      success: true,
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        message: '服务运行正常'
      }
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    return;
  }

  if (req.url === '/api/info' && req.method === 'GET') {
    const response = {
      success: true,
      data: {
        name: 'Web Proxy Service',
        version: '1.0.0',
        description: '中转服务测试版本',
        timestamp: new Date().toISOString()
      }
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    return;
  }

  if (req.url === '/test' && req.method === 'GET') {
    const response = {
      success: true,
      message: '测试接口正常工作',
      timestamp: new Date().toISOString()
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    return;
  }

  // 处理POST请求
  if (req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const data = body ? JSON.parse(body) : {};
        
        const response = {
          success: true,
          message: `POST请求处理成功: ${req.url}`,
          receivedData: data,
          timestamp: new Date().toISOString()
        };
        
        res.writeHead(200);
        res.end(JSON.stringify(response, null, 2));
      } catch (error) {
        const errorResponse = {
          success: false,
          error: {
            message: '请求数据解析失败',
            details: error.message
          }
        };
        res.writeHead(400);
        res.end(JSON.stringify(errorResponse, null, 2));
      }
    });
    return;
  }

  // 404处理
  const notFoundResponse = {
    success: false,
    error: {
      message: '接口不存在',
      path: req.url,
      method: req.method
    }
  };
  res.writeHead(404);
  res.end(JSON.stringify(notFoundResponse, null, 2));
});

const PORT = 3000;
const HOST = '127.0.0.1';

server.listen(PORT, HOST, () => {
  console.log(`✅ 服务器已启动`);
  console.log(`🌐 地址: http://${HOST}:${PORT}`);
  console.log(`🔍 测试地址:`);
  console.log(`   - 健康检查: http://${HOST}:${PORT}/health`);
  console.log(`   - API信息: http://${HOST}:${PORT}/api/info`);
  console.log(`   - 测试接口: http://${HOST}:${PORT}/test`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
});

server.on('error', (error) => {
  console.error('❌ 服务器启动失败:', error.message);
  if (error.code === 'EADDRINUSE') {
    console.log(`💡 端口 ${PORT} 已被占用，请尝试其他端口`);
  }
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

console.log('🚀 正在启动服务器...');
