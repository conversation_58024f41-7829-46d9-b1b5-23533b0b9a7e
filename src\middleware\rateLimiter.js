const rateLimit = require('express-rate-limit');
const config = require('../config');
const logger = require('../utils/logger');

// 创建限流中间件
const rateLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  message: config.rateLimit.message,
  standardHeaders: true, // 返回限流信息在 `RateLimit-*` headers
  legacyHeaders: false, // 禁用 `X-RateLimit-*` headers
  
  // 自定义键生成器（基于IP和用户代理）
  keyGenerator: (req) => {
    return `${req.ip}_${req.get('User-Agent') || 'unknown'}`;
  },

  // 跳过某些请求的限流
  skip: (req) => {
    // 跳过健康检查请求
    if (req.path === '/health') {
      return true;
    }
    
    // 跳过内部请求（如果有内部API密钥）
    if (req.headers['x-internal-key'] === process.env.INTERNAL_API_KEY) {
      return true;
    }
    
    return false;
  },

  // 限流触发时的处理
  onLimitReached: (req, res, options) => {
    logger.warn('Rate limit exceeded:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  },

  // 自定义响应
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      error: {
        message: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.round(config.rateLimit.windowMs / 1000),
        timestamp: new Date().toISOString()
      }
    });
  }
});

// 为脚本猫创建更宽松的限流策略
const tampermonkeyRateLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max * 2, // 脚本猫端口允许更多请求
  message: config.rateLimit.message,
  standardHeaders: true,
  legacyHeaders: false,
  
  keyGenerator: (req) => {
    // 如果有脚本标识，使用脚本ID作为键的一部分
    const scriptId = req.headers['x-tm-script'] || 'unknown';
    return `tm_${req.ip}_${scriptId}`;
  },

  skip: (req) => {
    if (req.path === '/health') {
      return true;
    }
    return false;
  },

  onLimitReached: (req, res, options) => {
    logger.warn('TamperMonkey rate limit exceeded:', {
      ip: req.ip,
      scriptId: req.headers['x-tm-script'],
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  },

  handler: (req, res) => {
    res.status(429).json({
      success: false,
      error: {
        message: '脚本请求过于频繁，请稍后再试',
        code: 'TM_RATE_LIMIT_EXCEEDED',
        retryAfter: Math.round(config.rateLimit.windowMs / 1000),
        timestamp: new Date().toISOString()
      }
    });
  }
});

module.exports = rateLimiter;
module.exports.tampermonkeyRateLimiter = tampermonkeyRateLimiter;
