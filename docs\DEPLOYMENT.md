# 部署文档

## 系统要求

- Node.js 16.0.0 或更高版本
- npm 或 yarn 包管理器
- 至少 512MB 内存
- 至少 1GB 磁盘空间

## 环境准备

### 1. 克隆项目
```bash
git clone <repository-url>
cd web-proxy
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
NODE_ENV=production
LOG_LEVEL=info
RATE_LIMIT_MAX=100
PROXY_TIMEOUT=30000
INTERNAL_API_KEY=your-secure-api-key-here
```

## 本地开发部署

### 开发模式启动
```bash
npm run dev
```

### 生产模式启动
```bash
npm start
```

### 测试
```bash
npm test
```

## Docker 部署

### 1. 创建 Dockerfile
```dockerfile
FROM node:16-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/
COPY .env ./

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 3000 3001 3002

# 设置用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
USER nodejs

# 启动应用
CMD ["npm", "start"]
```

### 2. 构建镜像
```bash
docker build -t web-proxy-service .
```

### 3. 运行容器
```bash
docker run -d \
  --name web-proxy \
  -p 3000:3000 \
  -p 3001:3001 \
  -p 3002:3002 \
  -v $(pwd)/logs:/app/logs \
  -e NODE_ENV=production \
  web-proxy-service
```

### 4. Docker Compose
创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  web-proxy:
    build: .
    ports:
      - "3000:3000"
      - "3001:3001" 
      - "3002:3002"
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - NODE_ENV=production
      - LOG_LEVEL=info
      - RATE_LIMIT_MAX=200
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：添加Redis用于数据存储
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

启动：
```bash
docker-compose up -d
```

## PM2 部署

### 1. 安装 PM2
```bash
npm install -g pm2
```

### 2. 创建 PM2 配置文件
创建 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'web-proxy-service',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      LOG_LEVEL: 'info'
    },
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### 3. 启动服务
```bash
# 生产环境启动
pm2 start ecosystem.config.js --env production

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
```

### 4. PM2 常用命令
```bash
# 查看状态
pm2 status

# 查看日志
pm2 logs web-proxy-service

# 重启服务
pm2 restart web-proxy-service

# 停止服务
pm2 stop web-proxy-service

# 删除服务
pm2 delete web-proxy-service

# 监控
pm2 monit
```

## Nginx 反向代理

### 1. 安装 Nginx
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

### 2. 配置 Nginx
创建 `/etc/nginx/sites-available/web-proxy`：
```nginx
upstream web_proxy_main {
    server 127.0.0.1:3000;
}

upstream web_proxy_tm {
    server 127.0.0.1:3001;
}

upstream web_proxy_ws {
    server 127.0.0.1:3002;
}

server {
    listen 80;
    server_name your-domain.com;

    # 主API服务
    location /api {
        proxy_pass http://web_proxy_main;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 代理服务
    location /proxy {
        proxy_pass http://web_proxy_main;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 脚本猫服务
    location /tampermonkey {
        proxy_pass http://web_proxy_tm;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers for TamperMonkey
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-TM-Script";
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # WebSocket服务
    location /ws {
        proxy_pass http://web_proxy_ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://web_proxy_main;
        access_log off;
    }

    # 静态文件（如果有）
    location /static {
        alias /path/to/static/files;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS配置（推荐）
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 其他配置同上...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 3. 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/web-proxy /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 系统服务配置

### 1. 创建 systemd 服务
创建 `/etc/systemd/system/web-proxy.service`：
```ini
[Unit]
Description=Web Proxy Service
After=network.target

[Service]
Type=simple
User=nodejs
WorkingDirectory=/opt/web-proxy
ExecStart=/usr/bin/node src/app.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=LOG_LEVEL=info

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/web-proxy/logs

[Install]
WantedBy=multi-user.target
```

### 2. 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable web-proxy
sudo systemctl start web-proxy
sudo systemctl status web-proxy
```

## 监控和日志

### 1. 日志轮转
创建 `/etc/logrotate.d/web-proxy`：
```
/opt/web-proxy/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nodejs nodejs
    postrotate
        systemctl reload web-proxy
    endscript
}
```

### 2. 监控脚本
创建健康检查脚本：
```bash
#!/bin/bash
# health-check.sh

HEALTH_URL="http://localhost:3000/health"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -eq 200 ]; then
    echo "Service is healthy"
    exit 0
else
    echo "Service is unhealthy (HTTP $RESPONSE)"
    exit 1
fi
```

### 3. 添加到 crontab
```bash
# 每分钟检查一次
* * * * * /path/to/health-check.sh
```

## 性能优化

### 1. Node.js 优化
```bash
# 增加内存限制
node --max-old-space-size=2048 src/app.js

# 启用集群模式
NODE_ENV=production pm2 start src/app.js -i max
```

### 2. 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" >> /etc/sysctl.conf
sysctl -p
```

## 故障排除

### 1. 常见问题
- 端口被占用：`lsof -i :3000`
- 内存不足：检查日志和监控指标
- 权限问题：确保用户有正确的文件权限

### 2. 日志分析
```bash
# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/app.log

# 搜索特定错误
grep "ERROR" logs/app.log
```

### 3. 性能分析
```bash
# 使用 PM2 监控
pm2 monit

# 查看进程状态
ps aux | grep node

# 查看网络连接
netstat -tulpn | grep :3000
```
