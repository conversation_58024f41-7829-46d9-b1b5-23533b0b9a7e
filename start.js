// 简化的启动脚本，用于测试基础功能
const http = require('http');
const url = require('url');
const querystring = require('querystring');

// 简单的路由处理
class SimpleRouter {
  constructor() {
    this.routes = new Map();
  }

  addRoute(method, path, handler) {
    const key = `${method.toUpperCase()}:${path}`;
    this.routes.set(key, handler);
  }

  handle(req, res) {
    const parsedUrl = url.parse(req.url, true);
    const key = `${req.method}:${parsedUrl.pathname}`;
    
    const handler = this.routes.get(key);
    if (handler) {
      handler(req, res, parsedUrl.query);
    } else {
      this.notFound(res);
    }
  }

  notFound(res) {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: {
        message: '接口不存在',
        code: 'NOT_FOUND'
      }
    }));
  }
}

// 创建路由器
const router = new SimpleRouter();

// 工具函数
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, { 
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-TM-Script'
  });
  res.end(JSON.stringify(data));
}

function getRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// 健康检查
router.addRoute('GET', '/health', (req, res) => {
  sendJSON(res, {
    success: true,
    data: {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0'
    }
  });
});

// API信息
router.addRoute('GET', '/api/info', (req, res) => {
  sendJSON(res, {
    success: true,
    data: {
      name: 'Web Proxy Service',
      version: '1.0.0',
      description: '中转服务 - 提供标准API并支持脚本猫多端口web控制',
      endpoints: {
        health: '/health',
        api: '/api/*',
        proxy: '/proxy/*',
        tampermonkey: '/tampermonkey/*'
      },
      features: [
        '多端口支持',
        '脚本猫集成',
        '请求代理',
        '实时通信'
      ]
    }
  });
});

// 简单的代理功能
router.addRoute('POST', '/proxy/request', async (req, res) => {
  try {
    const body = await getRequestBody(req);
    const { url: targetUrl, method = 'GET', headers = {} } = body;

    if (!targetUrl) {
      return sendJSON(res, {
        success: false,
        error: {
          message: 'URL参数是必需的',
          code: 'MISSING_URL'
        }
      }, 400);
    }

    // 这里是简化的代理实现
    console.log(`代理请求: ${method} ${targetUrl}`);
    
    sendJSON(res, {
      success: true,
      data: {
        message: '代理功能正在开发中',
        request: {
          url: targetUrl,
          method,
          headers
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    sendJSON(res, {
      success: false,
      error: {
        message: '代理请求失败',
        code: 'PROXY_ERROR',
        details: error.message
      }
    }, 500);
  }
});

// 脚本猫功能
router.addRoute('POST', '/tampermonkey/execute', async (req, res) => {
  try {
    const body = await getRequestBody(req);
    const { script, context = {} } = body;

    if (!script) {
      return sendJSON(res, {
        success: false,
        error: {
          message: '脚本内容是必需的',
          code: 'MISSING_SCRIPT'
        }
      }, 400);
    }

    // 简化的脚本执行（实际应该在沙箱中执行）
    console.log('执行脚本:', script.substring(0, 100) + '...');
    
    sendJSON(res, {
      success: true,
      data: {
        executionId: Math.random().toString(36).substr(2, 9),
        result: '脚本执行功能正在开发中',
        executionTime: Math.floor(Math.random() * 100),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    sendJSON(res, {
      success: false,
      error: {
        message: '脚本执行失败',
        code: 'SCRIPT_ERROR',
        details: error.message
      }
    }, 500);
  }
});

// 脚本猫存储
router.addRoute('POST', '/tampermonkey/storage', async (req, res) => {
  try {
    const body = await getRequestBody(req);
    const { action, key, value } = body;

    console.log(`存储操作: ${action} ${key}`);
    
    sendJSON(res, {
      success: true,
      data: {
        action,
        key,
        value: action === 'get' ? '存储功能正在开发中' : value,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    sendJSON(res, {
      success: false,
      error: {
        message: '存储操作失败',
        code: 'STORAGE_ERROR',
        details: error.message
      }
    }, 500);
  }
});

// OPTIONS处理（CORS预检）
router.addRoute('OPTIONS', '/api/info', (req, res) => {
  res.writeHead(200, {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-TM-Script'
  });
  res.end();
});

router.addRoute('OPTIONS', '/proxy/request', (req, res) => {
  res.writeHead(200, {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-TM-Script'
  });
  res.end();
});

router.addRoute('OPTIONS', '/tampermonkey/execute', (req, res) => {
  res.writeHead(200, {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-TM-Script'
  });
  res.end();
});

// 创建服务器
const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.url}`);
  router.handle(req, res);
});

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Web Proxy Service 已启动`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔍 健康检查: http://localhost:${PORT}/health`);
  console.log(`📖 API信息: http://localhost:${PORT}/api/info`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
