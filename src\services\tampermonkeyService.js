const axios = require('axios');
const { VM } = require('vm2');
const EventEmitter = require('events');
const logger = require('../utils/logger');
const config = require('../config');

class TampermonkeyService extends EventEmitter {
  constructor() {
    super();
    this.storage = new Map(); // 简单的内存存储，生产环境建议使用Redis
    this.eventListeners = new Map();
    this.scriptExecutions = new Map();
  }

  // 执行脚本
  async executeScript(script, context = {}, timeout = 10000, clientInfo = {}) {
    const executionId = require('crypto').randomUUID();
    const startTime = Date.now();

    try {
      // 创建安全的VM环境
      const vm = new VM({
        timeout,
        sandbox: {
          // 提供给脚本的API
          console: {
            log: (...args) => logger.info('[Script Log]', ...args),
            error: (...args) => logger.error('[Script Error]', ...args),
            warn: (...args) => logger.warn('[Script Warn]', ...args)
          },
          
          // 脚本猫常用API模拟
          GM: {
            getValue: (key, defaultValue) => this.getValue(key, defaultValue, clientInfo.scriptId),
            setValue: (key, value) => this.setValue(key, value, clientInfo.scriptId),
            deleteValue: (key) => this.deleteValue(key, clientInfo.scriptId),
            listValues: () => this.listValues(clientInfo.scriptId),
            
            xmlHttpRequest: (details) => this.xmlHttpRequest(details),
            
            notification: (text, title, image, onclick) => {
              logger.info('[GM Notification]', { text, title, scriptId: clientInfo.scriptId });
              return { text, title, image, timestamp: new Date().toISOString() };
            },
            
            openInTab: (url, options) => {
              logger.info('[GM OpenInTab]', { url, options, scriptId: clientInfo.scriptId });
              return { url, options, timestamp: new Date().toISOString() };
            },
            
            setClipboard: (data, info) => {
              logger.info('[GM SetClipboard]', { dataLength: data.length, scriptId: clientInfo.scriptId });
              return { success: true, dataLength: data.length };
            }
          },
          
          // 提供上下文数据
          context,
          
          // 时间相关
          Date,
          setTimeout: (fn, delay) => {
            if (delay > 5000) delay = 5000; // 限制最大延迟
            return setTimeout(fn, delay);
          },
          clearTimeout,
          
          // 基础工具
          JSON,
          Math,
          String,
          Number,
          Array,
          Object,
          RegExp,
          
          // 结果存储
          __result: null,
          __error: null
        }
      });

      // 包装脚本以捕获结果
      const wrappedScript = `
        try {
          const result = (function() {
            ${script}
          })();
          __result = result;
        } catch (error) {
          __error = error.message;
        }
      `;

      // 执行脚本
      vm.run(wrappedScript);

      const executionTime = Date.now() - startTime;
      
      // 记录执行信息
      this.scriptExecutions.set(executionId, {
        clientInfo,
        startTime,
        executionTime,
        success: !vm.sandbox.__error,
        timestamp: new Date().toISOString()
      });

      if (vm.sandbox.__error) {
        throw new Error(vm.sandbox.__error);
      }

      return {
        executionId,
        result: vm.sandbox.__result,
        executionTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      logger.error('Script execution failed:', {
        executionId,
        error: error.message,
        executionTime,
        clientInfo
      });

      throw error;
    }
  }

  // 存储操作
  async handleStorage(action, key, value, namespace = 'default', scriptId = 'unknown') {
    const storageKey = `${scriptId}:${namespace}:${key}`;
    
    switch (action) {
      case 'get':
        return this.storage.get(storageKey) || null;
        
      case 'set':
        this.storage.set(storageKey, value);
        return { success: true, key, value };
        
      case 'delete':
        const deleted = this.storage.delete(storageKey);
        return { success: deleted, key };
        
      case 'clear':
        const prefix = `${scriptId}:${namespace}:`;
        let cleared = 0;
        for (const [k] of this.storage) {
          if (k.startsWith(prefix)) {
            this.storage.delete(k);
            cleared++;
          }
        }
        return { success: true, cleared };
        
      case 'keys':
        const prefix2 = `${scriptId}:${namespace}:`;
        const keys = [];
        for (const [k] of this.storage) {
          if (k.startsWith(prefix2)) {
            keys.push(k.substring(prefix2.length));
          }
        }
        return { keys };
        
      default:
        throw new Error(`不支持的存储操作: ${action}`);
    }
  }

  // GM存储API实现
  getValue(key, defaultValue, scriptId) {
    const storageKey = `${scriptId}:default:${key}`;
    return this.storage.get(storageKey) || defaultValue;
  }

  setValue(key, value, scriptId) {
    const storageKey = `${scriptId}:default:${key}`;
    this.storage.set(storageKey, value);
    return true;
  }

  deleteValue(key, scriptId) {
    const storageKey = `${scriptId}:default:${key}`;
    return this.storage.delete(storageKey);
  }

  listValues(scriptId) {
    const prefix = `${scriptId}:default:`;
    const keys = [];
    for (const [k] of this.storage) {
      if (k.startsWith(prefix)) {
        keys.push(k.substring(prefix.length));
      }
    }
    return keys;
  }

  // GM网络请求实现
  async xmlHttpRequest(details) {
    const {
      method = 'GET',
      url,
      headers = {},
      data,
      responseType = 'text',
      timeout = 30000
    } = details;

    try {
      const response = await axios({
        method,
        url,
        headers,
        data,
        timeout,
        responseType: responseType === 'json' ? 'json' : 'text',
        validateStatus: () => true
      });

      return {
        status: response.status,
        statusText: response.statusText,
        responseHeaders: response.headers,
        response: response.data,
        responseText: typeof response.data === 'string' ? response.data : JSON.stringify(response.data),
        responseJSON: typeof response.data === 'object' ? response.data : null
      };

    } catch (error) {
      throw new Error(`网络请求失败: ${error.message}`);
    }
  }

  // 事件处理
  async handleEvent(action, event, data, listenerId, scriptId) {
    const eventKey = `${scriptId}:${event}`;
    
    switch (action) {
      case 'emit':
        this.emit(eventKey, data);
        return { success: true, event, data };
        
      case 'listen':
        const listener = (eventData) => {
          logger.logTampermonkey('event_triggered', {
            event,
            scriptId,
            data: eventData
          });
        };
        
        this.on(eventKey, listener);
        const id = require('crypto').randomUUID();
        this.eventListeners.set(id, { eventKey, listener });
        
        return { success: true, listenerId: id, event };
        
      case 'unlisten':
        if (listenerId && this.eventListeners.has(listenerId)) {
          const { eventKey: key, listener } = this.eventListeners.get(listenerId);
          this.off(key, listener);
          this.eventListeners.delete(listenerId);
          return { success: true, listenerId };
        }
        return { success: false, message: '监听器不存在' };
        
      default:
        throw new Error(`不支持的事件操作: ${action}`);
    }
  }

  // 跨域代理
  async corsProxy(url, method = 'GET', headers = {}, data, responseType = 'json') {
    try {
      const response = await axios({
        method,
        url,
        headers: {
          ...config.proxy.headers,
          ...headers
        },
        data,
        timeout: config.proxy.timeout,
        responseType: responseType === 'json' ? 'json' : 'text',
        validateStatus: () => true
      });

      return {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        url: response.config.url
      };

    } catch (error) {
      throw new Error(`跨域请求失败: ${error.message}`);
    }
  }

  // 获取执行统计
  getExecutionStats() {
    const stats = {
      totalExecutions: this.scriptExecutions.size,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0
    };

    let totalTime = 0;
    for (const [, execution] of this.scriptExecutions) {
      if (execution.success) {
        stats.successfulExecutions++;
      } else {
        stats.failedExecutions++;
      }
      totalTime += execution.executionTime;
    }

    if (stats.totalExecutions > 0) {
      stats.averageExecutionTime = Math.round(totalTime / stats.totalExecutions);
    }

    return stats;
  }

  // 清理过期数据
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    // 清理过期的执行记录
    for (const [id, execution] of this.scriptExecutions) {
      if (now - execution.startTime > maxAge) {
        this.scriptExecutions.delete(id);
      }
    }

    logger.info('TamperMonkey service cleanup completed', {
      remainingExecutions: this.scriptExecutions.size,
      storageSize: this.storage.size,
      eventListeners: this.eventListeners.size
    });
  }
}

module.exports = TampermonkeyService;
