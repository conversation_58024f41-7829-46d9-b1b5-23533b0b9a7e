# Web Proxy Service - 中转服务

一个功能强大的中转服务，提供标准的RESTful API接口，并支持脚本猫(TamperMonkey)多端口web控制。

## 🚀 特性

- **多端口支持**: 支持同时在多个端口运行不同的服务实例
- **脚本猫集成**: 专门为TamperMonkey脚本优化的API接口
- **请求代理**: 支持跨域请求代理，解决CORS问题
- **实时通信**: WebSocket支持，实现实时数据推送
- **安全防护**: 内置限流、错误处理、安全头设置
- **监控系统**: 完整的性能监控和健康检查
- **日志记录**: 详细的日志记录和分析

## 📋 目录结构

```
web-proxy/
├── src/
│   ├── app.js                 # 主应用入口
│   ├── config/
│   │   └── index.js          # 配置管理
│   ├── middleware/
│   │   ├── errorHandler.js   # 错误处理中间件
│   │   └── rateLimiter.js    # 限流中间件
│   ├── routes/
│   │   ├── api.js            # 通用API路由
│   │   ├── proxy.js          # 代理服务路由
│   │   └── tampermonkey.js   # 脚本猫专用路由
│   ├── services/
│   │   └── tampermonkeyService.js # 脚本猫服务
│   ├── utils/
│   │   ├── logger.js         # 日志工具
│   │   └── monitor.js        # 监控工具
│   └── websocket/
│       └── index.js          # WebSocket服务
├── logs/                     # 日志文件目录
├── package.json
├── .env.example             # 环境变量示例
└── README.md
```

## 🛠️ 安装与配置

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境变量示例文件并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：

```env
NODE_ENV=development
LOG_LEVEL=info
RATE_LIMIT_MAX=100
PROXY_TIMEOUT=30000
INTERNAL_API_KEY=your-secret-key
```

### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## 🌐 API 接口

### 基础信息

- **主服务端口**: 3000
- **脚本猫专用端口**: 3001  
- **WebSocket端口**: 3002

### 通用API (`/api`)

#### 获取服务信息
```http
GET /api/info
```

#### 获取服务状态
```http
GET /api/status
```

#### 数据处理
```http
POST /api/data
Content-Type: application/json

{
  "action": "process|transform|validate",
  "data": "要处理的数据",
  "options": {}
}
```

#### 批量操作
```http
POST /api/batch
Content-Type: application/json

{
  "operations": [
    {
      "id": "op1",
      "action": "process",
      "data": "数据1"
    }
  ]
}
```

### 代理服务 (`/proxy`)

#### 通用代理请求
```http
POST /proxy/request
Content-Type: application/json

{
  "url": "https://api.example.com/data",
  "method": "GET",
  "headers": {},
  "data": {},
  "timeout": 30000
}
```

#### GET请求快捷方式
```http
GET /proxy/get?url=https://api.example.com/data
```

#### 批量代理请求
```http
POST /proxy/batch
Content-Type: application/json

{
  "requests": [
    {
      "url": "https://api1.example.com",
      "method": "GET"
    }
  ],
  "concurrent": true
}
```

### 脚本猫API (`/tampermonkey`)

#### 脚本执行
```http
POST /tampermonkey/execute
Content-Type: application/json
X-TM-Script: your-script-id

{
  "script": "console.log('Hello World'); return 'result';",
  "context": {},
  "timeout": 10000
}
```

#### 数据存储
```http
POST /tampermonkey/storage
Content-Type: application/json
X-TM-Script: your-script-id

{
  "action": "set|get|delete|clear|keys",
  "key": "myKey",
  "value": "myValue",
  "namespace": "default"
}
```

#### 跨域代理
```http
POST /tampermonkey/cors-proxy
Content-Type: application/json
X-TM-Script: your-script-id

{
  "url": "https://api.example.com/data",
  "method": "GET",
  "headers": {},
  "responseType": "json"
}
```

## 🔌 WebSocket 连接

连接到WebSocket服务：

```javascript
const ws = new WebSocket('ws://localhost:3002');

ws.onopen = function() {
  console.log('WebSocket连接已建立');
  
  // 加入房间
  ws.send(JSON.stringify({
    type: 'join_room',
    data: { room: 'myRoom' }
  }));
};

ws.onmessage = function(event) {
  const message = JSON.parse(event.data);
  console.log('收到消息:', message);
};
```

### WebSocket 消息类型

- `join_room`: 加入房间
- `leave_room`: 离开房间  
- `broadcast`: 广播消息到房间
- `private_message`: 发送私人消息
- `script_event`: 脚本事件
- `ping/pong`: 心跳检测

## 🔧 脚本猫集成示例

### 基础使用

```javascript
// ==UserScript==
// @name         Web Proxy Service 示例
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  使用中转服务的示例脚本
// <AUTHOR>
// @match        https://example.com/*
// @grant        GM_xmlhttpRequest
// ==/UserScript==

(function() {
    'use strict';
    
    const API_BASE = 'http://localhost:3001/tampermonkey';
    const SCRIPT_ID = 'my-script-v1.0';
    
    // 执行远程脚本
    function executeRemoteScript(script) {
        return fetch(`${API_BASE}/execute`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-TM-Script': SCRIPT_ID
            },
            body: JSON.stringify({
                script: script,
                context: { url: window.location.href }
            })
        }).then(res => res.json());
    }
    
    // 存储数据
    function setValue(key, value) {
        return fetch(`${API_BASE}/storage`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-TM-Script': SCRIPT_ID
            },
            body: JSON.stringify({
                action: 'set',
                key: key,
                value: value
            })
        }).then(res => res.json());
    }
    
    // 跨域请求
    function corsRequest(url, options = {}) {
        return fetch(`${API_BASE}/cors-proxy`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-TM-Script': SCRIPT_ID
            },
            body: JSON.stringify({
                url: url,
                method: options.method || 'GET',
                headers: options.headers || {},
                data: options.data
            })
        }).then(res => res.json());
    }
    
    // 使用示例
    corsRequest('https://api.github.com/users/octocat')
        .then(result => {
            console.log('GitHub API响应:', result.data);
            return setValue('github_user', result.data);
        })
        .then(() => {
            console.log('数据已保存');
        });
})();
```

## 📊 监控与健康检查

### 健康检查
```http
GET /health
```

### 获取监控指标
```http
GET /api/status
```

### 监控指标包括

- 请求统计（总数、成功率、失败率）
- 性能指标（响应时间、吞吐量）
- 系统资源（CPU、内存使用率）
- 错误统计（错误类型、最近错误）
- WebSocket连接数

## 🚀 部署

### Docker 部署

```dockerfile
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY src/ ./src/
COPY .env ./

EXPOSE 3000 3001 3002

CMD ["npm", "start"]
```

### PM2 部署

```bash
npm install -g pm2
pm2 start src/app.js --name web-proxy
pm2 save
pm2 startup
```

## 🔒 安全注意事项

1. **API密钥**: 在生产环境中设置强密码的`INTERNAL_API_KEY`
2. **CORS配置**: 根据需要限制允许的来源域名
3. **限流设置**: 根据服务器性能调整限流参数
4. **日志安全**: 确保日志文件不包含敏感信息
5. **脚本执行**: 脚本执行在沙箱环境中，但仍需谨慎处理用户输入

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。
