const winston = require('winston');
const path = require('path');
const fs = require('fs');
const config = require('../config');

// 确保日志目录存在
const logDir = path.dirname(config.logging.file.filename);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ level, message, timestamp, stack }) => {
    if (stack) {
      return `${timestamp} [${level.toUpperCase()}]: ${message}\n${stack}`;
    }
    return `${timestamp} [${level.toUpperCase()}]: ${message}`;
  })
);

// 创建传输器数组
const transports = [];

// 控制台传输器
if (config.logging.console.enabled) {
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        logFormat
      )
    })
  );
}

// 文件传输器
if (config.logging.file.enabled) {
  transports.push(
    new winston.transports.File({
      filename: config.logging.file.filename,
      maxsize: config.logging.file.maxsize,
      maxFiles: config.logging.file.maxFiles,
      format: logFormat
    })
  );

  // 错误日志单独文件
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: config.logging.file.maxsize,
      maxFiles: config.logging.file.maxFiles,
      format: logFormat
    })
  );
}

// 创建logger实例
const logger = winston.createLogger({
  level: config.logging.level,
  transports,
  exitOnError: false
});

// 添加请求日志方法
logger.logRequest = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.url,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    timestamp: new Date().toISOString()
  };

  if (res.statusCode >= 400) {
    logger.warn('HTTP Request', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// 添加脚本猫专用日志方法
logger.logTampermonkey = (action, data) => {
  logger.info(`[TamperMonkey] ${action}`, {
    action,
    data,
    timestamp: new Date().toISOString()
  });
};

module.exports = logger;
