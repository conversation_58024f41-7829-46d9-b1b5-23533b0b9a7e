# API 文档

## 概述

Web Proxy Service 提供三个主要的API端点：

- `/api` - 通用API服务
- `/proxy` - 代理服务
- `/tampermonkey` - 脚本猫专用API

所有API都返回统一的JSON格式响应。

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "message": "错误描述",
    "code": "ERROR_CODE",
    "details": "详细错误信息",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## 通用API (`/api`)

### GET /api/info
获取服务基本信息

**响应示例:**
```json
{
  "success": true,
  "data": {
    "name": "Web Proxy Service",
    "version": "1.0.0",
    "description": "中转服务",
    "endpoints": {
      "api": "/api",
      "proxy": "/proxy",
      "tampermonkey": "/tampermonkey"
    },
    "features": ["多端口支持", "脚本猫集成", "请求代理"]
  }
}
```

### GET /api/status
获取服务运行状态

**响应示例:**
```json
{
  "success": true,
  "data": {
    "uptime": 3600,
    "memory": {
      "rss": 50331648,
      "heapTotal": 20971520,
      "heapUsed": 15728640
    },
    "cpu": {
      "user": 1000000,
      "system": 500000
    },
    "platform": "linux",
    "nodeVersion": "v16.20.0"
  }
}
```

### GET /api/config
获取配置信息（已过滤敏感信息）

**响应示例:**
```json
{
  "success": true,
  "data": {
    "ports": [
      {
        "port": 3000,
        "name": "main",
        "description": "主要API服务端口"
      }
    ],
    "rateLimit": {
      "windowMs": 900000,
      "max": 100
    }
  }
}
```

### POST /api/data
数据处理接口

**请求参数:**
```json
{
  "action": "process|transform|validate",
  "data": "要处理的数据",
  "options": {}
}
```

**参数说明:**
- `action` (string, required): 操作类型
  - `process`: 数据处理
  - `transform`: 数据转换
  - `validate`: 数据验证
- `data` (any, required): 要处理的数据
- `options` (object, optional): 处理选项

**响应示例:**
```json
{
  "success": true,
  "data": {
    "processed": true,
    "originalData": "input data",
    "processedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### POST /api/batch
批量操作接口

**请求参数:**
```json
{
  "operations": [
    {
      "id": "op1",
      "action": "process",
      "data": "数据1"
    },
    {
      "id": "op2", 
      "action": "transform",
      "data": "数据2"
    }
  ]
}
```

**参数说明:**
- `operations` (array, required): 操作列表，最多10个
  - `id` (string, required): 操作ID
  - `action` (string, required): 操作类型
  - `data` (any, required): 操作数据

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total": 2,
    "successful": 2,
    "failed": 0,
    "results": [
      {
        "id": "op1",
        "success": true,
        "result": "process completed for op1"
      }
    ]
  }
}
```

## 代理服务 (`/proxy`)

### POST /proxy/request
通用代理请求

**请求参数:**
```json
{
  "url": "https://api.example.com/data",
  "method": "GET|POST|PUT|DELETE|PATCH",
  "headers": {},
  "data": {},
  "timeout": 30000,
  "followRedirects": true
}
```

**参数说明:**
- `url` (string, required): 目标URL
- `method` (string, optional): HTTP方法，默认GET
- `headers` (object, optional): 请求头
- `data` (any, optional): 请求数据
- `timeout` (number, optional): 超时时间(ms)，默认30000
- `followRedirects` (boolean, optional): 是否跟随重定向，默认true

**响应示例:**
```json
{
  "success": true,
  "proxy": {
    "url": "https://api.example.com/data",
    "method": "GET",
    "statusCode": 200,
    "statusText": "OK",
    "headers": {},
    "data": {},
    "responseTime": 150
  }
}
```

### GET /proxy/get
GET请求快捷方式

**查询参数:**
- `url` (string, required): 目标URL

**示例:**
```
GET /proxy/get?url=https://api.github.com/users/octocat
```

### POST /proxy/batch
批量代理请求

**请求参数:**
```json
{
  "requests": [
    {
      "url": "https://api1.example.com",
      "method": "GET"
    },
    {
      "url": "https://api2.example.com", 
      "method": "POST",
      "data": {"key": "value"}
    }
  ],
  "concurrent": false
}
```

**参数说明:**
- `requests` (array, required): 请求列表，最多5个
- `concurrent` (boolean, optional): 是否并发执行，默认false

## 脚本猫API (`/tampermonkey`)

### 请求头
所有脚本猫API请求建议包含以下头：
```
X-TM-Script: your-script-id
Content-Type: application/json
```

### GET /tampermonkey/info
获取脚本猫服务信息

### POST /tampermonkey/execute
执行脚本

**请求参数:**
```json
{
  "script": "console.log('Hello'); return 'result';",
  "context": {},
  "timeout": 10000,
  "scriptId": "my-script",
  "scriptName": "My Script"
}
```

**参数说明:**
- `script` (string, required): 要执行的JavaScript代码，最大1MB
- `context` (object, optional): 传递给脚本的上下文数据
- `timeout` (number, optional): 执行超时时间(ms)，默认10000
- `scriptId` (string, optional): 脚本ID
- `scriptName` (string, optional): 脚本名称

**响应示例:**
```json
{
  "success": true,
  "data": {
    "executionId": "uuid",
    "result": "script return value",
    "executionTime": 50
  }
}
```

### POST /tampermonkey/storage
数据存储操作

**请求参数:**
```json
{
  "action": "set|get|delete|clear|keys",
  "key": "myKey",
  "value": "myValue", 
  "namespace": "default"
}
```

**参数说明:**
- `action` (string, required): 操作类型
  - `set`: 设置值
  - `get`: 获取值
  - `delete`: 删除值
  - `clear`: 清空命名空间
  - `keys`: 获取所有键
- `key` (string, conditional): 键名，get/set/delete时必需
- `value` (any, conditional): 值，set时必需
- `namespace` (string, optional): 命名空间，默认"default"

### POST /tampermonkey/events
事件系统

**请求参数:**
```json
{
  "action": "emit|listen|unlisten",
  "event": "eventName",
  "data": {},
  "listenerId": "listener-id"
}
```

### POST /tampermonkey/cors-proxy
跨域代理请求

**请求参数:**
```json
{
  "url": "https://api.example.com/data",
  "method": "GET",
  "headers": {},
  "data": {},
  "responseType": "json|text|blob|arraybuffer"
}
```

### GET /tampermonkey/utils
获取工具数据

**响应示例:**
```json
{
  "success": true,
  "data": {
    "timestamp": 1640995200000,
    "iso": "2024-01-01T00:00:00.000Z",
    "random": 0.123456789,
    "uuid": "uuid-string",
    "userAgent": "Mozilla/5.0...",
    "ip": "127.0.0.1"
  }
}
```

## 错误代码

| 代码 | 描述 |
|------|------|
| VALIDATION_ERROR | 请求参数验证失败 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 |
| PROXY_ERROR | 代理请求失败 |
| SCRIPT_EXECUTION_ERROR | 脚本执行失败 |
| STORAGE_ERROR | 存储操作失败 |
| EVENT_ERROR | 事件操作失败 |
| NOT_FOUND | 资源不存在 |
| INTERNAL_ERROR | 内部服务器错误 |

## 限流规则

- 默认限流：100请求/15分钟
- 脚本猫端口：200请求/15分钟
- 健康检查和内部API不受限流影响

## 安全注意事项

1. 脚本执行在沙箱环境中，有时间和资源限制
2. 代理请求会记录日志，注意隐私保护
3. 存储数据按脚本ID隔离
4. WebSocket连接有心跳检测机制
