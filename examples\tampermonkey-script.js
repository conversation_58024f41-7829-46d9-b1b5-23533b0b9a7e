// ==UserScript==
// @name         Web Proxy Service 集成示例
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  展示如何使用Web Proxy Service的各种功能
// <AUTHOR> Name
// @match        https://example.com/*
// @match        https://*.example.com/*
// @grant        GM_xmlhttpRequest
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_deleteValue
// @grant        GM_notification
// @connect      localhost
// @connect      api.github.com
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置
    const CONFIG = {
        API_BASE: 'http://localhost:3001/tampermonkey',
        SCRIPT_ID: 'web-proxy-example-v1.0',
        WS_URL: 'ws://localhost:3002'
    };
    
    // Web Proxy Service 客户端类
    class WebProxyClient {
        constructor(config) {
            this.config = config;
            this.ws = null;
        }
        
        // 通用请求方法
        async request(endpoint, data = {}, method = 'POST') {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: method,
                    url: `${this.config.API_BASE}${endpoint}`,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-TM-Script': this.config.SCRIPT_ID
                    },
                    data: JSON.stringify(data),
                    onload: function(response) {
                        try {
                            const result = JSON.parse(response.responseText);
                            if (result.success) {
                                resolve(result.data);
                            } else {
                                reject(new Error(result.error.message));
                            }
                        } catch (e) {
                            reject(new Error('解析响应失败: ' + e.message));
                        }
                    },
                    onerror: function(error) {
                        reject(new Error('请求失败: ' + error.statusText));
                    }
                });
            });
        }
        
        // 执行远程脚本
        async executeScript(script, context = {}) {
            return this.request('/execute', {
                script: script,
                context: context,
                timeout: 10000
            });
        }
        
        // 存储操作
        async setValue(key, value, namespace = 'default') {
            return this.request('/storage', {
                action: 'set',
                key: key,
                value: value,
                namespace: namespace
            });
        }
        
        async getValue(key, namespace = 'default') {
            return this.request('/storage', {
                action: 'get',
                key: key,
                namespace: namespace
            });
        }
        
        async deleteValue(key, namespace = 'default') {
            return this.request('/storage', {
                action: 'delete',
                key: key,
                namespace: namespace
            });
        }
        
        async listKeys(namespace = 'default') {
            return this.request('/storage', {
                action: 'keys',
                namespace: namespace
            });
        }
        
        // 跨域请求
        async corsRequest(url, options = {}) {
            return this.request('/cors-proxy', {
                url: url,
                method: options.method || 'GET',
                headers: options.headers || {},
                data: options.data,
                responseType: options.responseType || 'json'
            });
        }
        
        // 事件系统
        async emitEvent(event, data) {
            return this.request('/events', {
                action: 'emit',
                event: event,
                data: data
            });
        }
        
        // 获取工具数据
        async getUtils() {
            return this.request('/utils', {}, 'GET');
        }
        
        // WebSocket连接
        connectWebSocket() {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                return this.ws;
            }
            
            this.ws = new WebSocket(this.config.WS_URL);
            
            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                
                // 加入脚本专用房间
                this.ws.send(JSON.stringify({
                    type: 'join_room',
                    data: { room: this.config.SCRIPT_ID }
                }));
            };
            
            this.ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (e) {
                    console.error('WebSocket消息解析失败:', e);
                }
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                // 5秒后重连
                setTimeout(() => this.connectWebSocket(), 5000);
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
            };
            
            return this.ws;
        }
        
        // 处理WebSocket消息
        handleWebSocketMessage(message) {
            console.log('收到WebSocket消息:', message);
            
            switch (message.type) {
                case 'welcome':
                    console.log('WebSocket欢迎消息:', message);
                    break;
                    
                case 'broadcast':
                    console.log('收到广播消息:', message.data);
                    break;
                    
                case 'script_event':
                    this.handleScriptEvent(message);
                    break;
                    
                default:
                    console.log('未知消息类型:', message.type);
            }
        }
        
        // 处理脚本事件
        handleScriptEvent(message) {
            const { event, payload } = message;
            
            switch (event) {
                case 'page_update':
                    console.log('页面更新事件:', payload);
                    break;
                    
                case 'user_action':
                    console.log('用户操作事件:', payload);
                    break;
                    
                default:
                    console.log('未知脚本事件:', event, payload);
            }
        }
        
        // 发送WebSocket消息
        sendWebSocketMessage(type, data) {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type, data }));
            } else {
                console.warn('WebSocket未连接');
            }
        }
    }
    
    // 创建客户端实例
    const client = new WebProxyClient(CONFIG);
    
    // 示例功能函数
    async function demonstrateFeatures() {
        try {
            console.log('=== Web Proxy Service 功能演示 ===');
            
            // 1. 获取工具数据
            console.log('1. 获取工具数据...');
            const utils = await client.getUtils();
            console.log('工具数据:', utils);
            
            // 2. 存储数据
            console.log('2. 存储数据...');
            await client.setValue('last_visit', new Date().toISOString());
            await client.setValue('visit_count', (await getVisitCount()) + 1);
            
            // 3. 读取数据
            console.log('3. 读取数据...');
            const lastVisit = await client.getValue('last_visit');
            const visitCount = await client.getValue('visit_count');
            console.log(`上次访问: ${lastVisit}, 访问次数: ${visitCount}`);
            
            // 4. 跨域请求示例
            console.log('4. 跨域请求示例...');
            const githubUser = await client.corsRequest('https://api.github.com/users/octocat');
            console.log('GitHub用户信息:', githubUser);
            
            // 5. 执行远程脚本
            console.log('5. 执行远程脚本...');
            const scriptResult = await client.executeScript(`
                const pageInfo = {
                    title: document.title,
                    url: window.location.href,
                    timestamp: new Date().toISOString()
                };
                return pageInfo;
            `, { userAgent: navigator.userAgent });
            console.log('脚本执行结果:', scriptResult);
            
            // 6. 发送事件
            console.log('6. 发送事件...');
            await client.emitEvent('page_loaded', {
                url: window.location.href,
                title: document.title,
                timestamp: new Date().toISOString()
            });
            
            // 7. 连接WebSocket
            console.log('7. 连接WebSocket...');
            client.connectWebSocket();
            
            console.log('=== 功能演示完成 ===');
            
        } catch (error) {
            console.error('功能演示失败:', error);
            GM_notification('Web Proxy Service 演示失败: ' + error.message, 'Error');
        }
    }
    
    // 获取访问次数
    async function getVisitCount() {
        try {
            const count = await client.getValue('visit_count');
            return count || 0;
        } catch (error) {
            return 0;
        }
    }
    
    // 页面增强功能
    function enhancePage() {
        // 添加一个浮动按钮来触发功能演示
        const button = document.createElement('button');
        button.textContent = 'Web Proxy Demo';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        
        button.addEventListener('click', demonstrateFeatures);
        document.body.appendChild(button);
        
        // 页面加载时自动运行一次演示
        setTimeout(demonstrateFeatures, 1000);
    }
    
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', enhancePage);
    } else {
        enhancePage();
    }
    
    // 监听页面变化（SPA应用）
    let lastUrl = location.href;
    new MutationObserver(() => {
        const url = location.href;
        if (url !== lastUrl) {
            lastUrl = url;
            console.log('页面URL变化:', url);
            
            // 发送页面变化事件
            client.emitEvent('page_change', {
                from: lastUrl,
                to: url,
                timestamp: new Date().toISOString()
            }).catch(console.error);
        }
    }).observe(document, { subtree: true, childList: true });
    
    // 全局暴露客户端（用于控制台调试）
    window.webProxyClient = client;
    
    console.log('Web Proxy Service 脚本已加载');
    console.log('使用 window.webProxyClient 访问客户端API');
    
})();
