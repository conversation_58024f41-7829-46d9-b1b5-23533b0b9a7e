const express = require('express');
const axios = require('axios');
const Joi = require('joi');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

// 代理请求验证schema
const proxySchema = Joi.object({
  url: Joi.string().uri().required(),
  method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').default('GET'),
  headers: Joi.object().optional(),
  data: Joi.any().optional(),
  timeout: Joi.number().min(1000).max(60000).optional(),
  followRedirects: Joi.boolean().default(true)
});

// 请求验证中间件
function validateProxyRequest(req, res, next) {
  const { error } = proxySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '代理请求参数验证失败',
        details: error.details.map(detail => detail.message),
        code: 'PROXY_VALIDATION_ERROR'
      }
    });
  }
  next();
}

// 通用代理接口
router.post('/request', validateProxyRequest, asyncHandler(async (req, res) => {
  const { 
    url, 
    method = 'GET', 
    headers = {}, 
    data, 
    timeout = config.proxy.timeout,
    followRedirects = true 
  } = req.body;

  logger.info('Proxy request:', { 
    url, 
    method, 
    hasData: !!data,
    timeout,
    clientIP: req.ip 
  });

  try {
    // 准备请求配置
    const requestConfig = {
      method: method.toLowerCase(),
      url,
      timeout,
      maxRedirects: followRedirects ? config.proxy.maxRedirects : 0,
      headers: {
        ...config.proxy.headers,
        ...headers,
        // 移除可能导致问题的headers
        'host': undefined,
        'content-length': undefined
      },
      validateStatus: () => true // 接受所有状态码
    };

    // 添加请求数据
    if (data && ['post', 'put', 'patch'].includes(method.toLowerCase())) {
      requestConfig.data = data;
    }

    // 发送代理请求
    const startTime = Date.now();
    const response = await axios(requestConfig);
    const responseTime = Date.now() - startTime;

    // 记录响应日志
    logger.info('Proxy response:', {
      url,
      statusCode: response.status,
      responseTime: `${responseTime}ms`,
      dataSize: response.data ? JSON.stringify(response.data).length : 0
    });

    // 返回代理响应
    res.status(200).json({
      success: true,
      proxy: {
        url,
        method,
        statusCode: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        responseTime,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Proxy request failed:', {
      url,
      method,
      error: error.message,
      code: error.code,
      clientIP: req.ip
    });

    // 处理不同类型的错误
    let errorMessage = '代理请求失败';
    let errorCode = 'PROXY_ERROR';

    if (error.code === 'ENOTFOUND') {
      errorMessage = '目标地址无法解析';
      errorCode = 'DNS_ERROR';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = '目标服务拒绝连接';
      errorCode = 'CONNECTION_REFUSED';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = '请求超时';
      errorCode = 'TIMEOUT_ERROR';
    } else if (error.response) {
      // 目标服务返回了错误响应
      return res.status(200).json({
        success: true,
        proxy: {
          url,
          method,
          statusCode: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data,
          timestamp: new Date().toISOString()
        }
      });
    }

    res.status(500).json({
      success: false,
      error: {
        message: errorMessage,
        code: errorCode,
        details: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
}));

// GET代理快捷接口
router.get('/get', asyncHandler(async (req, res) => {
  const { url, ...params } = req.query;
  
  if (!url) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'URL参数是必需的',
        code: 'MISSING_URL'
      }
    });
  }

  // 转换为标准代理请求
  req.body = {
    url,
    method: 'GET',
    headers: req.headers
  };

  // 调用通用代理处理
  return router.handle(req, res);
}));

// 批量代理请求
const batchProxySchema = Joi.object({
  requests: Joi.array().items(proxySchema).min(1).max(5).required(),
  concurrent: Joi.boolean().default(false)
});

router.post('/batch', asyncHandler(async (req, res) => {
  const { error } = batchProxySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '批量代理请求参数验证失败',
        details: error.details.map(detail => detail.message),
        code: 'BATCH_PROXY_VALIDATION_ERROR'
      }
    });
  }

  const { requests, concurrent = false } = req.body;
  
  logger.info('Batch proxy request:', { 
    requestCount: requests.length, 
    concurrent,
    clientIP: req.ip 
  });

  const results = [];
  const startTime = Date.now();

  try {
    if (concurrent) {
      // 并发执行
      const promises = requests.map(async (request, index) => {
        try {
          const response = await axios({
            method: request.method.toLowerCase(),
            url: request.url,
            headers: { ...config.proxy.headers, ...request.headers },
            data: request.data,
            timeout: request.timeout || config.proxy.timeout,
            maxRedirects: request.followRedirects ? config.proxy.maxRedirects : 0,
            validateStatus: () => true
          });

          return {
            index,
            success: true,
            url: request.url,
            method: request.method,
            statusCode: response.status,
            data: response.data
          };
        } catch (error) {
          return {
            index,
            success: false,
            url: request.url,
            method: request.method,
            error: error.message
          };
        }
      });

      const responses = await Promise.all(promises);
      results.push(...responses);
    } else {
      // 顺序执行
      for (let i = 0; i < requests.length; i++) {
        const request = requests[i];
        try {
          const response = await axios({
            method: request.method.toLowerCase(),
            url: request.url,
            headers: { ...config.proxy.headers, ...request.headers },
            data: request.data,
            timeout: request.timeout || config.proxy.timeout,
            maxRedirects: request.followRedirects ? config.proxy.maxRedirects : 0,
            validateStatus: () => true
          });

          results.push({
            index: i,
            success: true,
            url: request.url,
            method: request.method,
            statusCode: response.status,
            data: response.data
          });
        } catch (error) {
          results.push({
            index: i,
            success: false,
            url: request.url,
            method: request.method,
            error: error.message
          });
        }
      }
    }

    const totalTime = Date.now() - startTime;
    const summary = {
      total: requests.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      totalTime: `${totalTime}ms`,
      concurrent,
      results
    };

    res.json({
      success: true,
      message: '批量代理请求完成',
      data: summary,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Batch proxy request failed:', error);
    res.status(500).json({
      success: false,
      error: {
        message: '批量代理请求失败',
        code: 'BATCH_PROXY_ERROR',
        details: error.message
      }
    });
  }
}));

module.exports = router;
