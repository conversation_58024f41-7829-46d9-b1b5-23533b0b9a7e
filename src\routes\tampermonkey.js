const express = require('express');
const Joi = require('joi');
const { asyncHandler } = require('../middleware/errorHandler');
const { tampermonkeyRateLimiter } = require('../middleware/rateLimiter');
const logger = require('../utils/logger');
const TampermonkeyService = require('../services/tampermonkeyService');

const router = express.Router();

// 应用脚本猫专用限流
router.use(tampermonkeyRateLimiter);

// 脚本猫服务实例
const tmService = new TampermonkeyService();

// 脚本执行验证schema
const scriptExecuteSchema = Joi.object({
  script: Joi.string().max(1024 * 1024).required(), // 最大1MB
  context: Joi.object().optional(),
  timeout: Joi.number().min(1000).max(30000).default(10000),
  scriptId: Joi.string().optional(),
  scriptName: Joi.string().optional()
});

// 脚本猫信息接口
router.get('/info', asyncHandler(async (req, res) => {
  const info = {
    name: 'TamperMonkey Integration Service',
    version: '1.0.0',
    features: [
      '脚本执行',
      '数据存储',
      '跨域请求',
      'WebSocket支持',
      '事件监听'
    ],
    limits: {
      maxScriptSize: '1MB',
      scriptTimeout: '30秒',
      rateLimit: '200请求/15分钟'
    },
    endpoints: {
      execute: '/tampermonkey/execute',
      storage: '/tampermonkey/storage',
      events: '/tampermonkey/events',
      websocket: '/tampermonkey/ws'
    }
  };

  logger.logTampermonkey('info_request', { ip: req.ip, userAgent: req.get('User-Agent') });
  
  res.json({
    success: true,
    message: '脚本猫服务信息',
    data: info,
    timestamp: new Date().toISOString()
  });
}));

// 脚本执行接口
router.post('/execute', asyncHandler(async (req, res) => {
  const { error } = scriptExecuteSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '脚本执行参数验证失败',
        details: error.details.map(detail => detail.message),
        code: 'SCRIPT_VALIDATION_ERROR'
      }
    });
  }

  const { script, context = {}, timeout, scriptId, scriptName } = req.body;
  const clientInfo = {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    scriptId: req.headers['x-tm-script'] || scriptId,
    timestamp: new Date().toISOString()
  };

  logger.logTampermonkey('script_execute_request', {
    ...clientInfo,
    scriptName,
    scriptLength: script.length,
    hasContext: Object.keys(context).length > 0
  });

  try {
    const result = await tmService.executeScript(script, context, timeout, clientInfo);
    
    logger.logTampermonkey('script_execute_success', {
      ...clientInfo,
      executionTime: result.executionTime,
      hasResult: !!result.result
    });

    res.json({
      success: true,
      message: '脚本执行成功',
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.logTampermonkey('script_execute_error', {
      ...clientInfo,
      error: error.message
    });

    res.status(500).json({
      success: false,
      error: {
        message: '脚本执行失败',
        code: 'SCRIPT_EXECUTION_ERROR',
        details: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
}));

// 数据存储接口
const storageSchema = Joi.object({
  action: Joi.string().valid('get', 'set', 'delete', 'clear', 'keys').required(),
  key: Joi.string().when('action', {
    is: Joi.string().valid('get', 'set', 'delete'),
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  value: Joi.any().when('action', {
    is: 'set',
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  namespace: Joi.string().default('default')
});

router.post('/storage', asyncHandler(async (req, res) => {
  const { error } = storageSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '存储操作参数验证失败',
        details: error.details.map(detail => detail.message),
        code: 'STORAGE_VALIDATION_ERROR'
      }
    });
  }

  const { action, key, value, namespace } = req.body;
  const scriptId = req.headers['x-tm-script'] || 'unknown';
  
  logger.logTampermonkey('storage_request', {
    action,
    key,
    namespace,
    scriptId,
    ip: req.ip
  });

  try {
    const result = await tmService.handleStorage(action, key, value, namespace, scriptId);
    
    res.json({
      success: true,
      message: `存储${action}操作成功`,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Storage operation failed:', error);
    res.status(500).json({
      success: false,
      error: {
        message: '存储操作失败',
        code: 'STORAGE_ERROR',
        details: error.message
      }
    });
  }
}));

// 事件系统接口
const eventSchema = Joi.object({
  action: Joi.string().valid('emit', 'listen', 'unlisten').required(),
  event: Joi.string().required(),
  data: Joi.any().optional(),
  listenerId: Joi.string().optional()
});

router.post('/events', asyncHandler(async (req, res) => {
  const { error } = eventSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '事件操作参数验证失败',
        details: error.details.map(detail => detail.message),
        code: 'EVENT_VALIDATION_ERROR'
      }
    });
  }

  const { action, event, data, listenerId } = req.body;
  const scriptId = req.headers['x-tm-script'] || 'unknown';
  
  logger.logTampermonkey('event_request', {
    action,
    event,
    scriptId,
    ip: req.ip
  });

  try {
    const result = await tmService.handleEvent(action, event, data, listenerId, scriptId);
    
    res.json({
      success: true,
      message: `事件${action}操作成功`,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Event operation failed:', error);
    res.status(500).json({
      success: false,
      error: {
        message: '事件操作失败',
        code: 'EVENT_ERROR',
        details: error.message
      }
    });
  }
}));

// 跨域请求代理（专为脚本猫优化）
const corsProxySchema = Joi.object({
  url: Joi.string().uri().required(),
  method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').default('GET'),
  headers: Joi.object().optional(),
  data: Joi.any().optional(),
  responseType: Joi.string().valid('json', 'text', 'blob', 'arraybuffer').default('json')
});

router.post('/cors-proxy', asyncHandler(async (req, res) => {
  const { error } = corsProxySchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '跨域代理参数验证失败',
        details: error.details.map(detail => detail.message),
        code: 'CORS_PROXY_VALIDATION_ERROR'
      }
    });
  }

  const { url, method, headers, data, responseType } = req.body;
  const scriptId = req.headers['x-tm-script'] || 'unknown';
  
  logger.logTampermonkey('cors_proxy_request', {
    url,
    method,
    scriptId,
    ip: req.ip
  });

  try {
    const result = await tmService.corsProxy(url, method, headers, data, responseType);
    
    res.json({
      success: true,
      message: '跨域请求成功',
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('CORS proxy failed:', error);
    res.status(500).json({
      success: false,
      error: {
        message: '跨域请求失败',
        code: 'CORS_PROXY_ERROR',
        details: error.message
      }
    });
  }
}));

// 脚本猫专用工具接口
router.get('/utils', asyncHandler(async (req, res) => {
  const utils = {
    timestamp: Date.now(),
    iso: new Date().toISOString(),
    random: Math.random(),
    uuid: require('crypto').randomUUID(),
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    scriptId: req.headers['x-tm-script']
  };

  res.json({
    success: true,
    message: '工具数据获取成功',
    data: utils,
    timestamp: new Date().toISOString()
  });
}));

module.exports = router;
