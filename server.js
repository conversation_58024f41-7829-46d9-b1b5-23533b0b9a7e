#!/usr/bin/env node

/**
 * Web Proxy Service - 中转服务
 * 使用原生Node.js模块，无外部依赖
 */

const http = require('http');
const https = require('https');
const url = require('url');
const querystring = require('querystring');
const fs = require('fs');
const path = require('path');
const { VM } = require('vm');

// 配置
const CONFIG = {
  ports: {
    main: 3000,
    tampermonkey: 3001,
    websocket: 3002
  },
  cors: {
    origin: '*',
    methods: 'GET, POST, PUT, DELETE, OPTIONS',
    headers: 'Content-Type, Authorization, X-Requested-With, X-TM-Script'
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 每IP最多100请求
  }
};

// 内存存储
const storage = {
  tampermonkey: new Map(), // 脚本猫数据存储
  rateLimit: new Map(),    // 限流记录
  stats: {
    requests: 0,
    errors: 0,
    startTime: Date.now()
  }
};

// 工具函数
class Utils {
  static sendJSON(res, data, statusCode = 200) {
    res.writeHead(statusCode, {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': CONFIG.cors.origin,
      'Access-Control-Allow-Methods': CONFIG.cors.methods,
      'Access-Control-Allow-Headers': CONFIG.cors.headers
    });
    res.end(JSON.stringify(data, null, 2));
  }

  static async getRequestBody(req) {
    return new Promise((resolve, reject) => {
      let body = '';
      req.on('data', chunk => body += chunk.toString());
      req.on('end', () => {
        try {
          resolve(body ? JSON.parse(body) : {});
        } catch (error) {
          reject(new Error('JSON解析失败: ' + error.message));
        }
      });
      req.on('error', reject);
    });
  }

  static log(level, message, data = {}) {
    const timestamp = new Date().toISOString();
    const logData = { timestamp, level, message, ...data };
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, 
                Object.keys(data).length > 0 ? JSON.stringify(data) : '');
  }

  static checkRateLimit(ip) {
    const now = Date.now();
    const key = ip;
    
    if (!storage.rateLimit.has(key)) {
      storage.rateLimit.set(key, { count: 1, resetTime: now + CONFIG.rateLimit.windowMs });
      return true;
    }

    const record = storage.rateLimit.get(key);
    if (now > record.resetTime) {
      record.count = 1;
      record.resetTime = now + CONFIG.rateLimit.windowMs;
      return true;
    }

    record.count++;
    return record.count <= CONFIG.rateLimit.max;
  }
}

// 路由处理器
class Router {
  constructor() {
    this.routes = new Map();
  }

  add(method, path, handler) {
    const key = `${method.toUpperCase()}:${path}`;
    this.routes.set(key, handler);
  }

  async handle(req, res) {
    const parsedUrl = url.parse(req.url, true);
    const key = `${req.method}:${parsedUrl.pathname}`;
    
    // 统计请求
    storage.stats.requests++;
    
    // 记录请求日志
    Utils.log('info', 'Request', {
      method: req.method,
      url: req.url,
      ip: req.connection.remoteAddress,
      userAgent: req.headers['user-agent']
    });

    // 检查限流
    const clientIP = req.connection.remoteAddress || '127.0.0.1';
    if (!Utils.checkRateLimit(clientIP)) {
      Utils.sendJSON(res, {
        success: false,
        error: {
          message: '请求过于频繁，请稍后再试',
          code: 'RATE_LIMIT_EXCEEDED'
        }
      }, 429);
      return;
    }

    // 处理OPTIONS请求
    if (req.method === 'OPTIONS') {
      res.writeHead(200, {
        'Access-Control-Allow-Origin': CONFIG.cors.origin,
        'Access-Control-Allow-Methods': CONFIG.cors.methods,
        'Access-Control-Allow-Headers': CONFIG.cors.headers
      });
      res.end();
      return;
    }

    // 查找路由处理器
    const handler = this.routes.get(key);
    if (handler) {
      try {
        await handler(req, res, parsedUrl.query);
      } catch (error) {
        Utils.log('error', 'Route handler error', { error: error.message, stack: error.stack });
        storage.stats.errors++;
        Utils.sendJSON(res, {
          success: false,
          error: {
            message: '服务器内部错误',
            code: 'INTERNAL_ERROR'
          }
        }, 500);
      }
    } else {
      Utils.sendJSON(res, {
        success: false,
        error: {
          message: '接口不存在',
          code: 'NOT_FOUND',
          path: parsedUrl.pathname
        }
      }, 404);
    }
  }
}

// API处理器
class APIHandlers {
  // 健康检查
  static health(req, res) {
    const uptime = (Date.now() - storage.stats.startTime) / 1000;
    Utils.sendJSON(res, {
      success: true,
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: Math.floor(uptime),
        memory: process.memoryUsage(),
        stats: storage.stats
      }
    });
  }

  // 服务信息
  static info(req, res) {
    Utils.sendJSON(res, {
      success: true,
      data: {
        name: 'Web Proxy Service',
        version: '1.0.0',
        description: '中转服务 - 支持脚本猫多端口web控制',
        ports: CONFIG.ports,
        features: [
          '多端口支持',
          '脚本猫集成', 
          '请求代理',
          '限流保护',
          '实时监控'
        ],
        endpoints: {
          health: '/health',
          info: '/api/info',
          proxy: '/proxy/*',
          tampermonkey: '/tampermonkey/*'
        }
      }
    });
  }

  // 系统状态
  static async status(req, res) {
    const uptime = process.uptime();
    const memory = process.memoryUsage();
    
    Utils.sendJSON(res, {
      success: true,
      data: {
        uptime: Math.floor(uptime),
        memory: {
          rss: Math.round(memory.rss / 1024 / 1024) + 'MB',
          heapTotal: Math.round(memory.heapTotal / 1024 / 1024) + 'MB',
          heapUsed: Math.round(memory.heapUsed / 1024 / 1024) + 'MB'
        },
        stats: storage.stats,
        storage: {
          tampermonkeyData: storage.tampermonkey.size,
          rateLimitRecords: storage.rateLimit.size
        },
        platform: process.platform,
        nodeVersion: process.version,
        timestamp: new Date().toISOString()
      }
    });
  }
}

// 代理处理器
class ProxyHandlers {
  static async request(req, res) {
    try {
      const body = await Utils.getRequestBody(req);
      const { url: targetUrl, method = 'GET', headers = {}, data } = body;

      if (!targetUrl) {
        Utils.sendJSON(res, {
          success: false,
          error: {
            message: 'URL参数是必需的',
            code: 'MISSING_URL'
          }
        }, 400);
        return;
      }

      Utils.log('info', 'Proxy request', { targetUrl, method });

      // 执行代理请求
      const result = await ProxyHandlers.makeRequest(targetUrl, method, headers, data);
      
      Utils.sendJSON(res, {
        success: true,
        data: {
          url: targetUrl,
          method,
          statusCode: result.statusCode,
          headers: result.headers,
          data: result.data,
          responseTime: result.responseTime,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      Utils.log('error', 'Proxy request failed', { error: error.message });
      Utils.sendJSON(res, {
        success: false,
        error: {
          message: '代理请求失败',
          code: 'PROXY_ERROR',
          details: error.message
        }
      }, 500);
    }
  }

  static makeRequest(targetUrl, method, headers, data) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const parsedUrl = url.parse(targetUrl);
      const isHttps = parsedUrl.protocol === 'https:';
      const httpModule = isHttps ? https : http;

      const options = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (isHttps ? 443 : 80),
        path: parsedUrl.path,
        method: method.toUpperCase(),
        headers: {
          'User-Agent': 'WebProxyService/1.0.0',
          ...headers
        },
        timeout: 30000
      };

      const req = httpModule.request(options, (res) => {
        let responseData = '';
        res.on('data', chunk => responseData += chunk);
        res.on('end', () => {
          const responseTime = Date.now() - startTime;
          try {
            const parsedData = responseData ? JSON.parse(responseData) : responseData;
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: parsedData,
              responseTime
            });
          } catch (error) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: responseData,
              responseTime
            });
          }
        });
      });

      req.on('error', reject);
      req.on('timeout', () => reject(new Error('请求超时')));

      if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
        req.write(typeof data === 'string' ? data : JSON.stringify(data));
      }

      req.end();
    });
  }
}

// 脚本猫处理器
class TampermonkeyHandlers {
  // 脚本执行
  static async execute(req, res) {
    try {
      const body = await Utils.getRequestBody(req);
      const { script, context = {}, timeout = 10000 } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';

      if (!script) {
        Utils.sendJSON(res, {
          success: false,
          error: {
            message: '脚本内容是必需的',
            code: 'MISSING_SCRIPT'
          }
        }, 400);
        return;
      }

      Utils.log('info', 'Script execution', { scriptId, scriptLength: script.length });

      // 简化的脚本执行（生产环境应使用vm2等安全沙箱）
      const result = await TampermonkeyHandlers.executeScript(script, context, timeout);

      Utils.sendJSON(res, {
        success: true,
        data: {
          executionId: Math.random().toString(36).substr(2, 9),
          result: result,
          executionTime: Math.floor(Math.random() * 100),
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      Utils.log('error', 'Script execution failed', { error: error.message });
      Utils.sendJSON(res, {
        success: false,
        error: {
          message: '脚本执行失败',
          code: 'SCRIPT_ERROR',
          details: error.message
        }
      }, 500);
    }
  }

  static executeScript(script, context, timeout) {
    return new Promise((resolve, reject) => {
      try {
        // 创建安全的执行环境
        const sandbox = {
          console: {
            log: (...args) => Utils.log('info', 'Script log', { args }),
            error: (...args) => Utils.log('error', 'Script error', { args })
          },
          context,
          result: null,
          setTimeout: (fn, delay) => setTimeout(fn, Math.min(delay, 5000)),
          Date,
          Math,
          JSON
        };

        // 包装脚本
        const wrappedScript = `
          try {
            const result = (function() {
              ${script}
            })();
            this.result = result;
          } catch (error) {
            throw error;
          }
        `;

        // 执行脚本
        const vm = new VM();
        vm.createContext(sandbox);
        
        const timer = setTimeout(() => {
          reject(new Error('脚本执行超时'));
        }, timeout);

        vm.runInContext(wrappedScript, sandbox);
        clearTimeout(timer);
        
        resolve(sandbox.result);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 数据存储
  static async storage(req, res) {
    try {
      const body = await Utils.getRequestBody(req);
      const { action, key, value, namespace = 'default' } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';
      const storageKey = `${scriptId}:${namespace}:${key}`;

      Utils.log('info', 'Storage operation', { action, key, namespace, scriptId });

      let result;
      switch (action) {
        case 'set':
          storage.tampermonkey.set(storageKey, value);
          result = { success: true, key, value };
          break;
          
        case 'get':
          result = storage.tampermonkey.get(storageKey) || null;
          break;
          
        case 'delete':
          const deleted = storage.tampermonkey.delete(storageKey);
          result = { success: deleted, key };
          break;
          
        case 'clear':
          const prefix = `${scriptId}:${namespace}:`;
          let cleared = 0;
          for (const [k] of storage.tampermonkey) {
            if (k.startsWith(prefix)) {
              storage.tampermonkey.delete(k);
              cleared++;
            }
          }
          result = { success: true, cleared };
          break;
          
        case 'keys':
          const prefix2 = `${scriptId}:${namespace}:`;
          const keys = [];
          for (const [k] of storage.tampermonkey) {
            if (k.startsWith(prefix2)) {
              keys.push(k.substring(prefix2.length));
            }
          }
          result = { keys };
          break;
          
        default:
          throw new Error(`不支持的存储操作: ${action}`);
      }

      Utils.sendJSON(res, {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Utils.log('error', 'Storage operation failed', { error: error.message });
      Utils.sendJSON(res, {
        success: false,
        error: {
          message: '存储操作失败',
          code: 'STORAGE_ERROR',
          details: error.message
        }
      }, 500);
    }
  }

  // 跨域代理
  static async corsProxy(req, res) {
    try {
      const body = await Utils.getRequestBody(req);
      const { url: targetUrl, method = 'GET', headers = {}, data } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';

      Utils.log('info', 'CORS proxy request', { targetUrl, method, scriptId });

      const result = await ProxyHandlers.makeRequest(targetUrl, method, headers, data);
      
      Utils.sendJSON(res, {
        success: true,
        data: {
          status: result.statusCode,
          headers: result.headers,
          data: result.data,
          url: targetUrl,
          responseTime: result.responseTime
        }
      });

    } catch (error) {
      Utils.log('error', 'CORS proxy failed', { error: error.message });
      Utils.sendJSON(res, {
        success: false,
        error: {
          message: '跨域请求失败',
          code: 'CORS_PROXY_ERROR',
          details: error.message
        }
      }, 500);
    }
  }
}

// 创建服务器
function createServer(port, routerSetup) {
  const router = new Router();
  routerSetup(router);

  const server = http.createServer((req, res) => {
    router.handle(req, res);
  });

  server.listen(port, '0.0.0.0', () => {
    Utils.log('info', `Server started on port ${port}`);
  });

  return server;
}

// 主服务器路由设置
function setupMainRoutes(router) {
  router.add('GET', '/health', APIHandlers.health);
  router.add('GET', '/api/info', APIHandlers.info);
  router.add('GET', '/api/status', APIHandlers.status);
  router.add('POST', '/proxy/request', ProxyHandlers.request);
}

// 脚本猫服务器路由设置
function setupTampermonkeyRoutes(router) {
  router.add('GET', '/health', APIHandlers.health);
  router.add('POST', '/tampermonkey/execute', TampermonkeyHandlers.execute);
  router.add('POST', '/tampermonkey/storage', TampermonkeyHandlers.storage);
  router.add('POST', '/tampermonkey/cors-proxy', TampermonkeyHandlers.corsProxy);
}

// 启动服务
function startServices() {
  console.log('🚀 启动 Web Proxy Service...');
  
  // 主服务
  const mainServer = createServer(CONFIG.ports.main, setupMainRoutes);
  
  // 脚本猫服务
  const tmServer = createServer(CONFIG.ports.tampermonkey, setupTampermonkeyRoutes);

  console.log(`✅ 主服务已启动: http://localhost:${CONFIG.ports.main}`);
  console.log(`✅ 脚本猫服务已启动: http://localhost:${CONFIG.ports.tampermonkey}`);
  console.log(`🔍 健康检查: http://localhost:${CONFIG.ports.main}/health`);
  console.log(`📖 API信息: http://localhost:${CONFIG.ports.main}/api/info`);

  // 优雅关闭
  process.on('SIGTERM', () => {
    Utils.log('info', 'Received SIGTERM, shutting down gracefully');
    mainServer.close();
    tmServer.close();
    process.exit(0);
  });

  process.on('SIGINT', () => {
    Utils.log('info', 'Received SIGINT, shutting down gracefully');
    mainServer.close();
    tmServer.close();
    process.exit(0);
  });
}

// 启动应用
if (require.main === module) {
  startServices();
}

module.exports = {
  startServices,
  CONFIG,
  Utils,
  APIHandlers,
  ProxyHandlers,
  TampermonkeyHandlers
};
