#!/usr/bin/env node

/**
 * Web Proxy Service - 完整版本
 * 包含Web测试界面
 */

const http = require('http');
const https = require('https');
const url = require('url');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动 Web Proxy Service 完整版本...');

// 配置
const CONFIG = {
  ports: {
    main: 3000,
    tampermonkey: 3001
  },
  cors: {
    origin: '*',
    methods: 'GET, POST, PUT, DELETE, OPTIONS',
    headers: 'Content-Type, Authorization, X-Requested-With, X-TM-Script'
  }
};

// 内存存储
const storage = {
  tampermonkey: new Map(),
  stats: {
    requests: 0,
    errors: 0,
    startTime: Date.now()
  }
};

// 工具函数
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': CONFIG.cors.origin,
    'Access-Control-Allow-Methods': CONFIG.cors.methods,
    'Access-Control-Allow-Headers': CONFIG.cors.headers
  });
  res.end(JSON.stringify(data, null, 2));
}

function sendHTML(res, html) {
  res.writeHead(200, {
    'Content-Type': 'text/html; charset=utf-8',
    'Access-Control-Allow-Origin': CONFIG.cors.origin
  });
  res.end(html);
}

async function getRequestBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(new Error('JSON解析失败: ' + error.message));
      }
    });
    req.on('error', reject);
  });
}

function log(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, 
              Object.keys(data).length > 0 ? JSON.stringify(data) : '');
}

// 路由处理器
async function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // 统计请求
  storage.stats.requests++;
  
  log('info', 'Request', {
    method,
    url: req.url,
    ip: req.connection.remoteAddress
  });

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': CONFIG.cors.origin,
      'Access-Control-Allow-Methods': CONFIG.cors.methods,
      'Access-Control-Allow-Headers': CONFIG.cors.headers
    });
    res.end();
    return;
  }

  try {
    // 路由分发
    if (pathname === '/' || pathname === '/index.html') {
      // 返回测试界面
      const html = fs.readFileSync('web-interface.html', 'utf8');
      sendHTML(res, html);
      return;
    }

    if (pathname === '/health') {
      const uptime = (Date.now() - storage.stats.startTime) / 1000;
      sendJSON(res, {
        success: true,
        data: {
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: Math.floor(uptime),
          memory: process.memoryUsage(),
          stats: storage.stats
        }
      });
      return;
    }

    if (pathname === '/api/info') {
      sendJSON(res, {
        success: true,
        data: {
          name: 'Web Proxy Service',
          version: '1.0.0',
          description: '中转服务 - 支持脚本猫多端口web控制',
          ports: CONFIG.ports,
          features: [
            '多端口支持',
            '脚本猫集成', 
            '请求代理',
            '实时监控',
            'Web测试界面'
          ]
        }
      });
      return;
    }

    if (pathname === '/api/status') {
      const uptime = process.uptime();
      const memory = process.memoryUsage();
      
      sendJSON(res, {
        success: true,
        data: {
          uptime: Math.floor(uptime),
          memory: {
            rss: Math.round(memory.rss / 1024 / 1024) + 'MB',
            heapTotal: Math.round(memory.heapTotal / 1024 / 1024) + 'MB',
            heapUsed: Math.round(memory.heapUsed / 1024 / 1024) + 'MB'
          },
          stats: storage.stats,
          storage: {
            tampermonkeyData: storage.tampermonkey.size
          },
          platform: process.platform,
          nodeVersion: process.version,
          timestamp: new Date().toISOString()
        }
      });
      return;
    }

    if (pathname === '/proxy/request' && method === 'POST') {
      const body = await getRequestBody(req);
      const { url: targetUrl, method: proxyMethod = 'GET', headers = {}, data } = body;

      if (!targetUrl) {
        sendJSON(res, {
          success: false,
          error: {
            message: 'URL参数是必需的',
            code: 'MISSING_URL'
          }
        }, 400);
        return;
      }

      log('info', 'Proxy request', { targetUrl, method: proxyMethod });

      try {
        const result = await makeProxyRequest(targetUrl, proxyMethod, headers, data);
        sendJSON(res, {
          success: true,
          data: {
            url: targetUrl,
            method: proxyMethod,
            statusCode: result.statusCode,
            headers: result.headers,
            data: result.data,
            responseTime: result.responseTime,
            timestamp: new Date().toISOString()
          }
        });
      } catch (error) {
        log('error', 'Proxy request failed', { error: error.message });
        sendJSON(res, {
          success: false,
          error: {
            message: '代理请求失败',
            code: 'PROXY_ERROR',
            details: error.message
          }
        }, 500);
      }
      return;
    }

    // 404处理
    sendJSON(res, {
      success: false,
      error: {
        message: '接口不存在',
        code: 'NOT_FOUND',
        path: pathname
      }
    }, 404);

  } catch (error) {
    log('error', 'Request handler error', { error: error.message });
    storage.stats.errors++;
    sendJSON(res, {
      success: false,
      error: {
        message: '服务器内部错误',
        code: 'INTERNAL_ERROR'
      }
    }, 500);
  }
}

// 脚本猫服务处理器
async function handleTampermonkeyRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  log('info', 'TamperMonkey request', { method, url: req.url });

  // 处理OPTIONS请求
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': CONFIG.cors.origin,
      'Access-Control-Allow-Methods': CONFIG.cors.methods,
      'Access-Control-Allow-Headers': CONFIG.cors.headers
    });
    res.end();
    return;
  }

  try {
    if (pathname === '/health') {
      sendJSON(res, {
        success: true,
        data: {
          status: 'ok',
          service: 'tampermonkey',
          timestamp: new Date().toISOString()
        }
      });
      return;
    }

    if (pathname === '/tampermonkey/execute' && method === 'POST') {
      const body = await getRequestBody(req);
      const { script, context = {} } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';

      if (!script) {
        sendJSON(res, {
          success: false,
          error: {
            message: '脚本内容是必需的',
            code: 'MISSING_SCRIPT'
          }
        }, 400);
        return;
      }

      log('info', 'Script execution', { scriptId, scriptLength: script.length });

      // 简化的脚本执行
      try {
        const result = executeScript(script, context);
        sendJSON(res, {
          success: true,
          data: {
            executionId: Math.random().toString(36).substr(2, 9),
            result: result,
            executionTime: Math.floor(Math.random() * 100),
            timestamp: new Date().toISOString()
          }
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: {
            message: '脚本执行失败',
            code: 'SCRIPT_ERROR',
            details: error.message
          }
        }, 500);
      }
      return;
    }

    if (pathname === '/tampermonkey/storage' && method === 'POST') {
      const body = await getRequestBody(req);
      const { action, key, value, namespace = 'default' } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';
      const storageKey = `${scriptId}:${namespace}:${key}`;

      log('info', 'Storage operation', { action, key, namespace, scriptId });

      let result;
      switch (action) {
        case 'set':
          storage.tampermonkey.set(storageKey, value);
          result = { success: true, key, value };
          break;
          
        case 'get':
          result = storage.tampermonkey.get(storageKey) || null;
          break;
          
        case 'delete':
          const deleted = storage.tampermonkey.delete(storageKey);
          result = { success: deleted, key };
          break;
          
        default:
          throw new Error(`不支持的存储操作: ${action}`);
      }

      sendJSON(res, {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (pathname === '/tampermonkey/cors-proxy' && method === 'POST') {
      const body = await getRequestBody(req);
      const { url: targetUrl, method: proxyMethod = 'GET', headers = {}, data } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';

      log('info', 'CORS proxy request', { targetUrl, method: proxyMethod, scriptId });

      try {
        const result = await makeProxyRequest(targetUrl, proxyMethod, headers, data);
        sendJSON(res, {
          success: true,
          data: {
            status: result.statusCode,
            headers: result.headers,
            data: result.data,
            url: targetUrl,
            responseTime: result.responseTime
          }
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: {
            message: '跨域请求失败',
            code: 'CORS_PROXY_ERROR',
            details: error.message
          }
        }, 500);
      }
      return;
    }

    if (pathname === '/tampermonkey/analyze-page' && method === 'POST') {
      const body = await getRequestBody(req);
      const { url: targetUrl, selectors = [], extractData = true } = body;
      const scriptId = req.headers['x-tm-script'] || 'unknown';

      log('info', 'Page analysis request', { targetUrl, scriptId });

      try {
        const pageData = await analyzePage(targetUrl, selectors, extractData);
        sendJSON(res, {
          success: true,
          data: pageData
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: {
            message: '页面分析失败',
            code: 'PAGE_ANALYSIS_ERROR',
            details: error.message
          }
        }, 500);
      }
      return;
    }

    if (pathname === '/tampermonkey/generate-script' && method === 'POST') {
      const body = await getRequestBody(req);
      const {
        targetUrl,
        pageStructure,
        requirements,
        scriptType = 'basic',
        features = []
      } = body;

      log('info', 'Script generation request', { targetUrl, scriptType, features });

      try {
        const generatedScript = generateTampermonkeyScript({
          targetUrl,
          pageStructure,
          requirements,
          scriptType,
          features
        });

        sendJSON(res, {
          success: true,
          data: {
            script: generatedScript,
            metadata: {
              targetUrl,
              scriptType,
              features,
              generatedAt: new Date().toISOString()
            }
          }
        });
      } catch (error) {
        sendJSON(res, {
          success: false,
          error: {
            message: '脚本生成失败',
            code: 'SCRIPT_GENERATION_ERROR',
            details: error.message
          }
        }, 500);
      }
      return;
    }

    // 404处理
    sendJSON(res, {
      success: false,
      error: {
        message: '接口不存在',
        code: 'NOT_FOUND',
        path: pathname
      }
    }, 404);

  } catch (error) {
    log('error', 'TamperMonkey request error', { error: error.message });
    sendJSON(res, {
      success: false,
      error: {
        message: '服务器内部错误',
        code: 'INTERNAL_ERROR'
      }
    }, 500);
  }
}

// 代理请求函数
function makeProxyRequest(targetUrl, method, headers, data) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const parsedUrl = url.parse(targetUrl);
    const isHttps = parsedUrl.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.path,
      method: method.toUpperCase(),
      headers: {
        'User-Agent': 'WebProxyService/1.0.0',
        ...headers
      },
      timeout: 30000
    };

    const req = httpModule.request(options, (res) => {
      let responseData = '';
      res.on('data', chunk => responseData += chunk);
      res.on('end', () => {
        const responseTime = Date.now() - startTime;
        try {
          const parsedData = responseData ? JSON.parse(responseData) : responseData;
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsedData,
            responseTime
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData,
            responseTime
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('请求超时')));

    if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }

    req.end();
  });
}

// 简化的脚本执行
function executeScript(script, context) {
  // 注意：这是简化版本，生产环境应使用vm2等安全沙箱
  try {
    const func = new Function('context', `
      const console = {
        log: (...args) => ({ type: 'log', args }),
        error: (...args) => ({ type: 'error', args })
      };
      ${script}
    `);
    return func(context);
  } catch (error) {
    throw new Error('脚本执行错误: ' + error.message);
  }
}

// 页面分析函数
async function analyzePage(targetUrl, selectors = [], extractData = true) {
  try {
    // 获取页面HTML
    const pageResponse = await makeProxyRequest(targetUrl, 'GET', {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });

    const html = pageResponse.data;

    // 基础页面信息提取
    const analysis = {
      url: targetUrl,
      title: extractTitle(html),
      meta: extractMeta(html),
      forms: extractForms(html),
      links: extractLinks(html),
      scripts: extractScripts(html),
      elements: extractElements(html, selectors),
      structure: analyzePageStructure(html),
      suggestions: generateScriptSuggestions(html, targetUrl)
    };

    return analysis;
  } catch (error) {
    throw new Error('页面分析失败: ' + error.message);
  }
}

// 提取页面标题
function extractTitle(html) {
  const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
  return titleMatch ? titleMatch[1].trim() : '';
}

// 提取Meta信息
function extractMeta(html) {
  const metas = [];
  const metaRegex = /<meta[^>]+>/gi;
  let match;

  while ((match = metaRegex.exec(html)) !== null) {
    const metaTag = match[0];
    const name = (metaTag.match(/name=["']([^"']+)["']/i) || [])[1];
    const content = (metaTag.match(/content=["']([^"']+)["']/i) || [])[1];
    const property = (metaTag.match(/property=["']([^"']+)["']/i) || [])[1];

    if ((name || property) && content) {
      metas.push({
        name: name || property,
        content: content
      });
    }
  }

  return metas;
}

// 提取表单信息
function extractForms(html) {
  const forms = [];
  const formRegex = /<form[^>]*>(.*?)<\/form>/gis;
  let match;

  while ((match = formRegex.exec(html)) !== null) {
    const formHtml = match[0];
    const formContent = match[1];

    const action = (formHtml.match(/action=["']([^"']+)["']/i) || [])[1] || '';
    const method = (formHtml.match(/method=["']([^"']+)["']/i) || [])[1] || 'GET';

    // 提取输入字段
    const inputs = [];
    const inputRegex = /<input[^>]+>/gi;
    let inputMatch;

    while ((inputMatch = inputRegex.exec(formContent)) !== null) {
      const inputTag = inputMatch[0];
      const type = (inputTag.match(/type=["']([^"']+)["']/i) || [])[1] || 'text';
      const name = (inputTag.match(/name=["']([^"']+)["']/i) || [])[1];
      const id = (inputTag.match(/id=["']([^"']+)["']/i) || [])[1];
      const placeholder = (inputTag.match(/placeholder=["']([^"']+)["']/i) || [])[1];

      if (name || id) {
        inputs.push({ type, name, id, placeholder });
      }
    }

    forms.push({
      action,
      method: method.toUpperCase(),
      inputs
    });
  }

  return forms;
}

// 提取链接信息
function extractLinks(html) {
  const links = [];
  const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>(.*?)<\/a>/gi;
  let match;

  while ((match = linkRegex.exec(html)) !== null) {
    const href = match[1];
    const text = match[2].replace(/<[^>]+>/g, '').trim();

    if (href && text) {
      links.push({ href, text });
    }
  }

  return links.slice(0, 20); // 限制数量
}

// 提取脚本信息
function extractScripts(html) {
  const scripts = [];
  const scriptRegex = /<script[^>]*(?:src=["']([^"']+)["'])?[^>]*>/gi;
  let match;

  while ((match = scriptRegex.exec(html)) !== null) {
    const src = match[1];
    if (src) {
      scripts.push({ src, type: 'external' });
    }
  }

  return scripts;
}

// 提取指定元素
function extractElements(html, selectors) {
  const elements = {};

  // 简化的CSS选择器解析（仅支持基础选择器）
  selectors.forEach(selector => {
    if (selector.startsWith('#')) {
      // ID选择器
      const id = selector.substring(1);
      const regex = new RegExp(`<[^>]+id=["']${id}["'][^>]*>`, 'i');
      const match = html.match(regex);
      if (match) {
        elements[selector] = match[0];
      }
    } else if (selector.startsWith('.')) {
      // Class选择器
      const className = selector.substring(1);
      const regex = new RegExp(`<[^>]+class=["'][^"']*${className}[^"']*["'][^>]*>`, 'gi');
      const matches = html.match(regex);
      if (matches) {
        elements[selector] = matches.slice(0, 5); // 限制数量
      }
    }
  });

  return elements;
}

// 分析页面结构
function analyzePageStructure(html) {
  return {
    hasJQuery: html.includes('jquery'),
    hasReact: html.includes('react') || html.includes('React'),
    hasVue: html.includes('vue') || html.includes('Vue'),
    hasAngular: html.includes('angular') || html.includes('Angular'),
    isSPA: html.includes('router') || html.includes('history.pushState'),
    hasAjax: html.includes('XMLHttpRequest') || html.includes('fetch'),
    frameworks: detectFrameworks(html)
  };
}

// 检测前端框架
function detectFrameworks(html) {
  const frameworks = [];

  if (html.includes('bootstrap')) frameworks.push('Bootstrap');
  if (html.includes('element-ui')) frameworks.push('Element UI');
  if (html.includes('antd') || html.includes('ant-design')) frameworks.push('Ant Design');
  if (html.includes('layui')) frameworks.push('LayUI');

  return frameworks;
}

// 生成脚本建议
function generateScriptSuggestions(html, url) {
  const suggestions = [];

  // 基于页面内容生成建议
  if (html.includes('login') || html.includes('登录')) {
    suggestions.push({
      type: 'auto-login',
      description: '自动登录功能',
      priority: 'high'
    });
  }

  if (html.includes('form')) {
    suggestions.push({
      type: 'form-filler',
      description: '表单自动填充',
      priority: 'medium'
    });
  }

  if (html.includes('download') || html.includes('下载')) {
    suggestions.push({
      type: 'download-helper',
      description: '下载助手',
      priority: 'medium'
    });
  }

  if (html.includes('video') || html.includes('player')) {
    suggestions.push({
      type: 'video-enhancer',
      description: '视频增强功能',
      priority: 'high'
    });
  }

  return suggestions;
}

// 创建服务器
const mainServer = http.createServer(handleRequest);
const tmServer = http.createServer(handleTampermonkeyRequest);

// 启动服务
mainServer.listen(CONFIG.ports.main, '0.0.0.0', () => {
  console.log(`✅ 主服务已启动: http://localhost:${CONFIG.ports.main}`);
  console.log(`🌐 Web界面: http://localhost:${CONFIG.ports.main}`);
});

tmServer.listen(CONFIG.ports.tampermonkey, '0.0.0.0', () => {
  console.log(`✅ 脚本猫服务已启动: http://localhost:${CONFIG.ports.tampermonkey}`);
});

console.log('🔍 健康检查: http://localhost:3000/health');
console.log('📖 API信息: http://localhost:3000/api/info');
console.log('🎯 测试界面: http://localhost:3000');

// 优雅关闭
process.on('SIGTERM', () => {
  log('info', 'Received SIGTERM, shutting down gracefully');
  mainServer.close();
  tmServer.close();
  process.exit(0);
});

process.on('SIGINT', () => {
  log('info', 'Received SIGINT, shutting down gracefully');
  mainServer.close();
  tmServer.close();
  process.exit(0);
});

console.log('🚀 Web Proxy Service 启动完成!');
