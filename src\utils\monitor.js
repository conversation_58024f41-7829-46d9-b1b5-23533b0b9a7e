const os = require('os');
const logger = require('./logger');

class SystemMonitor {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byEndpoint: new Map()
      },
      performance: {
        averageResponseTime: 0,
        maxResponseTime: 0,
        minResponseTime: Infinity
      },
      system: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0
      },
      errors: {
        total: 0,
        byType: new Map(),
        recent: []
      }
    };

    this.responseTimes = [];
    this.maxRecentErrors = 100;
    
    // 启动系统监控
    this.startSystemMonitoring();
  }

  // 记录请求
  recordRequest(req, res, responseTime) {
    this.metrics.requests.total++;
    
    if (res.statusCode >= 200 && res.statusCode < 400) {
      this.metrics.requests.successful++;
    } else {
      this.metrics.requests.failed++;
    }

    // 按端点统计
    const endpoint = `${req.method} ${req.route?.path || req.path}`;
    const endpointStats = this.metrics.requests.byEndpoint.get(endpoint) || {
      count: 0,
      avgResponseTime: 0,
      errors: 0
    };
    
    endpointStats.count++;
    endpointStats.avgResponseTime = (endpointStats.avgResponseTime * (endpointStats.count - 1) + responseTime) / endpointStats.count;
    
    if (res.statusCode >= 400) {
      endpointStats.errors++;
    }
    
    this.metrics.requests.byEndpoint.set(endpoint, endpointStats);

    // 记录响应时间
    this.recordResponseTime(responseTime);
  }

  // 记录响应时间
  recordResponseTime(responseTime) {
    this.responseTimes.push(responseTime);
    
    // 保持最近1000个响应时间
    if (this.responseTimes.length > 1000) {
      this.responseTimes.shift();
    }

    // 更新性能指标
    this.updatePerformanceMetrics();
  }

  // 更新性能指标
  updatePerformanceMetrics() {
    if (this.responseTimes.length === 0) return;

    const sum = this.responseTimes.reduce((a, b) => a + b, 0);
    this.metrics.performance.averageResponseTime = sum / this.responseTimes.length;
    this.metrics.performance.maxResponseTime = Math.max(...this.responseTimes);
    this.metrics.performance.minResponseTime = Math.min(...this.responseTimes);
  }

  // 记录错误
  recordError(error, context = {}) {
    this.metrics.errors.total++;

    const errorType = error.name || 'UnknownError';
    const errorCount = this.metrics.errors.byType.get(errorType) || 0;
    this.metrics.errors.byType.set(errorType, errorCount + 1);

    // 记录最近的错误
    const errorRecord = {
      message: error.message,
      type: errorType,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString()
    };

    this.metrics.errors.recent.unshift(errorRecord);
    
    // 保持最近的错误记录
    if (this.metrics.errors.recent.length > this.maxRecentErrors) {
      this.metrics.errors.recent.pop();
    }
  }

  // 启动系统监控
  startSystemMonitoring() {
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000); // 每30秒更新一次

    // 立即更新一次
    this.updateSystemMetrics();
  }

  // 更新系统指标
  updateSystemMetrics() {
    // CPU使用率
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    this.metrics.system.cpuUsage = 100 - (totalIdle / totalTick * 100);

    // 内存使用率
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    this.metrics.system.memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;

    // 进程内存使用
    const processMemory = process.memoryUsage();
    this.metrics.system.processMemory = {
      rss: processMemory.rss,
      heapTotal: processMemory.heapTotal,
      heapUsed: processMemory.heapUsed,
      external: processMemory.external
    };
  }

  // 获取所有指标
  getMetrics() {
    return {
      ...this.metrics,
      system: {
        ...this.metrics.system,
        uptime: process.uptime(),
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        loadAverage: os.loadavg(),
        timestamp: new Date().toISOString()
      }
    };
  }

  // 获取健康状态
  getHealthStatus() {
    const metrics = this.getMetrics();
    const health = {
      status: 'healthy',
      checks: {},
      timestamp: new Date().toISOString()
    };

    // 检查内存使用率
    if (metrics.system.memoryUsage > 90) {
      health.status = 'unhealthy';
      health.checks.memory = 'critical';
    } else if (metrics.system.memoryUsage > 80) {
      health.checks.memory = 'warning';
    } else {
      health.checks.memory = 'ok';
    }

    // 检查CPU使用率
    if (metrics.system.cpuUsage > 90) {
      health.status = 'unhealthy';
      health.checks.cpu = 'critical';
    } else if (metrics.system.cpuUsage > 80) {
      health.checks.cpu = 'warning';
    } else {
      health.checks.cpu = 'ok';
    }

    // 检查错误率
    const errorRate = metrics.requests.total > 0 ? 
      (metrics.requests.failed / metrics.requests.total) * 100 : 0;
    
    if (errorRate > 10) {
      health.status = 'unhealthy';
      health.checks.errorRate = 'critical';
    } else if (errorRate > 5) {
      health.checks.errorRate = 'warning';
    } else {
      health.checks.errorRate = 'ok';
    }

    // 检查响应时间
    if (metrics.performance.averageResponseTime > 5000) {
      health.status = 'unhealthy';
      health.checks.responseTime = 'critical';
    } else if (metrics.performance.averageResponseTime > 2000) {
      health.checks.responseTime = 'warning';
    } else {
      health.checks.responseTime = 'ok';
    }

    return health;
  }

  // 重置指标
  resetMetrics() {
    this.metrics.requests = {
      total: 0,
      successful: 0,
      failed: 0,
      byEndpoint: new Map()
    };
    
    this.metrics.performance = {
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity
    };
    
    this.metrics.errors = {
      total: 0,
      byType: new Map(),
      recent: []
    };

    this.responseTimes = [];
    
    logger.info('监控指标已重置');
  }

  // 生成报告
  generateReport() {
    const metrics = this.getMetrics();
    const health = this.getHealthStatus();
    
    const report = {
      summary: {
        status: health.status,
        uptime: metrics.system.uptime,
        totalRequests: metrics.requests.total,
        successRate: metrics.requests.total > 0 ? 
          ((metrics.requests.successful / metrics.requests.total) * 100).toFixed(2) + '%' : '0%',
        averageResponseTime: metrics.performance.averageResponseTime.toFixed(2) + 'ms',
        memoryUsage: metrics.system.memoryUsage.toFixed(2) + '%',
        cpuUsage: metrics.system.cpuUsage.toFixed(2) + '%'
      },
      details: {
        requests: metrics.requests,
        performance: metrics.performance,
        system: metrics.system,
        errors: {
          total: metrics.errors.total,
          byType: Object.fromEntries(metrics.errors.byType),
          recentCount: metrics.errors.recent.length
        },
        health
      },
      timestamp: new Date().toISOString()
    };

    return report;
  }
}

// 创建全局监控实例
const monitor = new SystemMonitor();

module.exports = monitor;
