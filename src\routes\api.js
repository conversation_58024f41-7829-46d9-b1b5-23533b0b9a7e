const express = require('express');
const Joi = require('joi');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// 请求验证中间件
function validateRequest(schema) {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: {
          message: '请求参数验证失败',
          details: error.details.map(detail => detail.message),
          code: 'VALIDATION_ERROR'
        }
      });
    }
    next();
  };
}

// 通用响应格式化
function formatResponse(data, message = '操作成功') {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };
}

// API信息接口
router.get('/info', asyncHandler(async (req, res) => {
  const info = {
    name: 'Web Proxy Service',
    version: '1.0.0',
    description: '中转服务 - 提供标准API并支持脚本猫多端口web控制',
    endpoints: {
      api: '/api',
      proxy: '/proxy',
      tampermonkey: '/tampermonkey',
      health: '/health'
    },
    features: [
      '多端口支持',
      '脚本猫集成',
      '请求代理',
      '限流保护',
      '日志记录'
    ]
  };

  res.json(formatResponse(info, 'API信息获取成功'));
}));

// 服务状态接口
router.get('/status', asyncHandler(async (req, res) => {
  const status = {
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    platform: process.platform,
    nodeVersion: process.version,
    timestamp: new Date().toISOString()
  };

  res.json(formatResponse(status, '服务状态获取成功'));
}));

// 配置信息接口（敏感信息已过滤）
router.get('/config', asyncHandler(async (req, res) => {
  const config = require('../config');
  
  // 过滤敏感信息
  const safeConfig = {
    ports: config.ports.map(port => ({
      port: port.port,
      name: port.name,
      description: port.description
    })),
    rateLimit: {
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.max
    },
    proxy: {
      timeout: config.proxy.timeout,
      maxRedirects: config.proxy.maxRedirects
    }
  };

  res.json(formatResponse(safeConfig, '配置信息获取成功'));
}));

// 数据处理接口
const dataSchema = Joi.object({
  action: Joi.string().valid('process', 'transform', 'validate').required(),
  data: Joi.any().required(),
  options: Joi.object().optional()
});

router.post('/data', validateRequest(dataSchema), asyncHandler(async (req, res) => {
  const { action, data, options = {} } = req.body;

  logger.info('Data processing request:', { action, dataType: typeof data, options });

  let result;
  
  switch (action) {
    case 'process':
      // 数据处理逻辑
      result = {
        processed: true,
        originalData: data,
        processedAt: new Date().toISOString(),
        options
      };
      break;
      
    case 'transform':
      // 数据转换逻辑
      result = {
        transformed: true,
        originalData: data,
        transformedData: JSON.stringify(data), // 示例转换
        transformedAt: new Date().toISOString(),
        options
      };
      break;
      
    case 'validate':
      // 数据验证逻辑
      result = {
        valid: true,
        data,
        validatedAt: new Date().toISOString(),
        options
      };
      break;
      
    default:
      throw new Error(`不支持的操作: ${action}`);
  }

  res.json(formatResponse(result, `数据${action}操作完成`));
}));

// 批量操作接口
const batchSchema = Joi.object({
  operations: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      action: Joi.string().required(),
      data: Joi.any().required()
    })
  ).min(1).max(10).required()
});

router.post('/batch', validateRequest(batchSchema), asyncHandler(async (req, res) => {
  const { operations } = req.body;
  
  logger.info('Batch operation request:', { operationCount: operations.length });

  const results = [];
  
  for (const operation of operations) {
    try {
      // 模拟批量操作处理
      const result = {
        id: operation.id,
        success: true,
        action: operation.action,
        result: `${operation.action} completed for ${operation.id}`,
        processedAt: new Date().toISOString()
      };
      results.push(result);
    } catch (error) {
      results.push({
        id: operation.id,
        success: false,
        error: error.message,
        processedAt: new Date().toISOString()
      });
    }
  }

  const summary = {
    total: operations.length,
    successful: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    results
  };

  res.json(formatResponse(summary, '批量操作完成'));
}));

module.exports = router;
