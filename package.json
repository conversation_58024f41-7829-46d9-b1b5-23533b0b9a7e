{"name": "web-proxy-service", "version": "1.0.0", "description": "中转服务 - 提供标准API并支持脚本猫多端口web控制", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["proxy", "api", "tampermonkey", "web-service"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "axios": "^1.6.2", "ws": "^8.14.2", "vm2": "^3.9.19"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}