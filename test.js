// 简单测试
const http = require('http');

console.log('测试连接到服务器...');

const req = http.get('http://127.0.0.1:3000/health', (res) => {
  console.log(`状态码: ${res.statusCode}`);
  
  let data = '';
  res.on('data', chunk => data += chunk);
  res.on('end', () => {
    console.log('响应:', data);
    process.exit(0);
  });
});

req.on('error', (err) => {
  console.log('错误:', err.message);
  process.exit(1);
});

setTimeout(() => {
  console.log('超时');
  process.exit(1);
}, 3000);
