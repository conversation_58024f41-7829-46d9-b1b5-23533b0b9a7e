module.exports = {
  apps: [{
    name: 'web-proxy-service',
    script: 'src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    
    // 环境变量
    env: {
      NODE_ENV: 'development',
      LOG_LEVEL: 'debug'
    },
    env_production: {
      NODE_ENV: 'production',
      LOG_LEVEL: 'info',
      RATE_LIMIT_MAX: 200,
      PROXY_TIMEOUT: 30000
    },
    
    // 日志配置
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 性能配置
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    
    // 监控配置
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    
    // 重启配置
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // 健康检查
    health_check_grace_period: 3000,
    
    // 其他配置
    autorestart: true,
    vizion: false
  }],

  deploy: {
    production: {
      user: 'nodejs',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/web-proxy.git',
      path: '/opt/web-proxy',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
