// 简单的测试脚本来验证服务设置
const fs = require('fs');
const path = require('path');

console.log('=== Web Proxy Service 设置验证 ===');

// 检查必要的目录
const requiredDirs = ['src', 'logs'];
requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✓ 创建目录: ${dir}`);
  } else {
    console.log(`✓ 目录存在: ${dir}`);
  }
});

// 检查必要的文件
const requiredFiles = [
  'src/app.js',
  'src/config/index.js',
  'package.json'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✓ 文件存在: ${file}`);
  } else {
    console.log(`✗ 文件缺失: ${file}`);
  }
});

// 创建环境变量文件
if (!fs.existsSync('.env')) {
  const envContent = `NODE_ENV=development
LOG_LEVEL=info
RATE_LIMIT_MAX=100
PROXY_TIMEOUT=30000
INTERNAL_API_KEY=test-key-123
`;
  fs.writeFileSync('.env', envContent);
  console.log('✓ 创建 .env 文件');
} else {
  console.log('✓ .env 文件存在');
}

console.log('\n=== 设置验证完成 ===');
console.log('现在可以运行: npm install && npm start');
