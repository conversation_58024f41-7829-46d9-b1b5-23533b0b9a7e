# Web Proxy Service - 项目总结

## 🎯 项目概述

已成功搭建了一个功能完整的中转服务，提供标准的RESTful API接口，并专门支持脚本猫(TamperMonkey)多端口web控制。

## 📁 项目结构

```
web-proxy/
├── src/                          # 源代码目录
│   ├── app.js                   # 主应用入口，多端口服务管理
│   ├── config/
│   │   └── index.js            # 配置管理，支持环境变量覆盖
│   ├── middleware/
│   │   ├── errorHandler.js     # 统一错误处理中间件
│   │   └── rateLimiter.js      # 限流中间件，支持脚本猫专用限流
│   ├── routes/
│   │   ├── api.js              # 通用API路由（信息、状态、数据处理）
│   │   ├── proxy.js            # 代理服务路由（跨域请求、批量代理）
│   │   └── tampermonkey.js     # 脚本猫专用路由（脚本执行、存储、事件）
│   ├── services/
│   │   └── tampermonkeyService.js # 脚本猫服务核心逻辑
│   ├── utils/
│   │   ├── logger.js           # 日志工具，支持文件和控制台输出
│   │   └── monitor.js          # 系统监控工具，性能指标收集
│   └── websocket/
│       └── index.js            # WebSocket服务，实时通信支持
├── docs/                        # 文档目录
│   ├── API.md                  # 详细API文档
│   └── DEPLOYMENT.md           # 部署指南
├── examples/                    # 示例代码
│   └── tampermonkey-script.js  # 脚本猫集成示例
├── logs/                       # 日志文件目录（自动创建）
├── package.json                # 项目依赖和脚本
├── .env.example               # 环境变量示例
├── .gitignore                 # Git忽略文件
├── ecosystem.config.js        # PM2配置文件
├── test-setup.js             # 设置验证脚本
└── README.md                 # 项目说明文档
```

## 🚀 核心功能

### 1. 多端口服务架构
- **端口3000**: 主要API服务
- **端口3001**: 脚本猫专用服务
- **端口3002**: WebSocket实时通信

### 2. 通用API服务 (`/api`)
- 服务信息查询
- 系统状态监控
- 数据处理接口
- 批量操作支持

### 3. 代理服务 (`/proxy`)
- 跨域请求代理
- 支持所有HTTP方法
- 批量代理请求
- 请求超时控制

### 4. 脚本猫集成 (`/tampermonkey`)
- 安全脚本执行环境（VM2沙箱）
- 数据存储系统（按脚本ID隔离）
- 事件系统支持
- 跨域请求代理
- GM API模拟

### 5. WebSocket实时通信
- 房间管理
- 广播消息
- 私人消息
- 脚本事件传递
- 心跳检测

### 6. 安全与监控
- 请求限流保护
- 错误处理与日志
- 性能监控
- 健康检查
- 系统资源监控

## 🛠️ 技术栈

- **运行时**: Node.js 16+
- **框架**: Express.js
- **安全**: Helmet, CORS, 限流
- **日志**: Winston
- **WebSocket**: ws
- **脚本执行**: VM2
- **HTTP客户端**: Axios
- **参数验证**: Joi

## 📋 快速开始

### 1. 环境准备
```bash
# 确保已安装 Node.js 16+ 和 npm
node --version
npm --version
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
```bash
cp .env.example .env
# 编辑 .env 文件配置必要参数
```

### 4. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 5. 验证服务
```bash
# 检查健康状态
curl http://localhost:3000/health

# 检查API信息
curl http://localhost:3000/api/info
```

## 🔌 脚本猫集成示例

项目包含完整的脚本猫集成示例 (`examples/tampermonkey-script.js`)，展示了：

- 客户端封装类
- 脚本执行
- 数据存储
- 跨域请求
- WebSocket连接
- 事件系统
- 页面增强

## 📊 API接口概览

### 通用API
- `GET /api/info` - 服务信息
- `GET /api/status` - 系统状态
- `POST /api/data` - 数据处理
- `POST /api/batch` - 批量操作

### 代理服务
- `POST /proxy/request` - 通用代理
- `GET /proxy/get` - GET快捷方式
- `POST /proxy/batch` - 批量代理

### 脚本猫API
- `POST /tampermonkey/execute` - 脚本执行
- `POST /tampermonkey/storage` - 数据存储
- `POST /tampermonkey/events` - 事件系统
- `POST /tampermonkey/cors-proxy` - 跨域代理

## 🚀 部署选项

### 1. 本地部署
直接使用 `npm start` 启动

### 2. PM2部署
```bash
pm2 start ecosystem.config.js --env production
```

### 3. Docker部署
```bash
docker build -t web-proxy-service .
docker run -p 3000:3000 -p 3001:3001 -p 3002:3002 web-proxy-service
```

### 4. 系统服务
使用systemd配置为系统服务

## 🔒 安全特性

- 请求限流（可配置）
- 脚本执行沙箱
- 错误信息过滤
- CORS配置
- 安全头设置
- 日志记录

## 📈 监控功能

- 请求统计
- 响应时间监控
- 系统资源监控
- 错误统计
- 健康检查
- WebSocket连接监控

## 🔧 配置选项

所有配置都支持环境变量覆盖：

- 端口配置
- 限流设置
- 代理超时
- 日志级别
- 脚本执行限制
- WebSocket配置

## 📝 下一步建议

1. **安装Node.js**: 从 https://nodejs.org 下载安装
2. **安装依赖**: 运行 `npm install`
3. **配置环境**: 复制并编辑 `.env` 文件
4. **启动服务**: 运行 `npm start`
5. **测试功能**: 使用提供的示例脚本测试
6. **部署生产**: 选择合适的部署方式

## 🤝 支持与维护

- 详细的API文档在 `docs/API.md`
- 部署指南在 `docs/DEPLOYMENT.md`
- 示例代码在 `examples/` 目录
- 所有配置都有详细注释

项目已经完全搭建完成，具备了生产环境使用的所有必要功能和文档。
