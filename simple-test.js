// 最简单的测试
const http = require('http');

console.log('开始测试连接...');

// 测试连接
const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/health',
  method: 'GET'
};

console.log('发送请求到:', `http://${options.hostname}:${options.port}${options.path}`);

const req = http.request(options, (res) => {
  console.log('✅ 连接成功!');
  console.log('状态码:', res.statusCode);
  console.log('响应头:', res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('响应内容:', data);
    console.log('🎉 测试完成!');
    process.exit(0);
  });
});

req.on('error', (error) => {
  console.error('❌ 连接失败:', error.message);
  console.error('错误代码:', error.code);
  
  if (error.code === 'ECONNREFUSED') {
    console.log('💡 提示: 服务器可能未启动或端口被占用');
  }
  
  process.exit(1);
});

req.setTimeout(5000, () => {
  console.log('⏰ 请求超时');
  req.destroy();
  process.exit(1);
});

req.end();

console.log('请求已发送，等待响应...');
