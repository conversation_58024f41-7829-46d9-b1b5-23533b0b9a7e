# 服务配置
NODE_ENV=development
LOG_LEVEL=info

# 端口配置 (JSON格式)
# PORTS=[{"port":3000,"host":"0.0.0.0","name":"main","description":"主要API服务端口"},{"port":3001,"host":"0.0.0.0","name":"tampermonkey","description":"脚本猫专用端口"}]

# 限流配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# 代理配置
PROXY_TIMEOUT=30000
PROXY_MAX_REDIRECTS=5

# 安全配置
INTERNAL_API_KEY=your-internal-api-key-here

# 脚本猫配置
TM_SCRIPT_TIMEOUT=10000
TM_MAX_SCRIPT_SIZE=1048576
TM_WS_PORT=3002

# 数据库配置 (如果使用)
# DATABASE_URL=mongodb://localhost:27017/web-proxy
# REDIS_URL=redis://localhost:6379

# 日志配置
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=5242880
LOG_MAX_FILES=5

# CORS配置
CORS_ORIGIN=*
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With,X-TM-Script
