const logger = require('../utils/logger');

// 错误处理中间件
function errorHandler(err, req, res, next) {
  // 记录错误日志
  logger.error('Error occurred:', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // 设置默认错误状态码
  let statusCode = err.statusCode || err.status || 500;
  let message = err.message || '内部服务器错误';

  // 处理特定类型的错误
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = '请求参数验证失败';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    message = '未授权访问';
  } else if (err.name === 'CastError') {
    statusCode = 400;
    message = '无效的请求参数';
  } else if (err.code === 'ECONNREFUSED') {
    statusCode = 503;
    message = '目标服务不可用';
  } else if (err.code === 'ETIMEDOUT') {
    statusCode = 504;
    message = '请求超时';
  }

  // 构建错误响应
  const errorResponse = {
    success: false,
    error: {
      message,
      code: err.code || 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    }
  };

  // 在开发环境中包含堆栈信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = err.stack;
    errorResponse.error.details = err.details;
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
}

// 404处理中间件
function notFoundHandler(req, res) {
  logger.warn('404 Not Found:', {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  res.status(404).json({
    success: false,
    error: {
      message: '请求的资源不存在',
      code: 'NOT_FOUND',
      timestamp: new Date().toISOString()
    }
  });
}

// 异步错误捕获包装器
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

module.exports = {
  errorHandler,
  notFoundHandler,
  asyncHandler
};
