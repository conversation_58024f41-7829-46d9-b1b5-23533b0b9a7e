const path = require('path');

// 默认配置
const defaultConfig = {
  // 多端口配置
  ports: [
    {
      port: 3000,
      host: '0.0.0.0',
      name: 'main',
      description: '主要API服务端口',
      cors: {
        origin: '*',
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
      }
    },
    {
      port: 3001,
      host: '0.0.0.0',
      name: 'tampermonkey',
      description: '脚本猫专用端口',
      cors: {
        origin: '*',
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-TM-Script']
      }
    }
  ],

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: true,
      filename: path.join(__dirname, '../../logs/app.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    },
    console: {
      enabled: true,
      colorize: true
    }
  },

  // 限流配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 每个IP最多100个请求
    message: {
      error: 'Too many requests',
      message: '请求过于频繁，请稍后再试'
    }
  },

  // 代理配置
  proxy: {
    timeout: 30000, // 30秒超时
    maxRedirects: 5,
    headers: {
      'User-Agent': 'WebProxyService/1.0.0'
    }
  },

  // 脚本猫配置
  tampermonkey: {
    allowedOrigins: ['*'], // 允许的来源
    scriptTimeout: 10000, // 脚本执行超时时间
    maxScriptSize: 1024 * 1024, // 最大脚本大小 1MB
    enableWebSocket: true, // 启用WebSocket支持
    wsPort: 3002 // WebSocket端口
  },

  // 安全配置
  security: {
    helmet: {
      contentSecurityPolicy: false, // 为了支持脚本猫，暂时禁用CSP
      crossOriginEmbedderPolicy: false
    }
  }
};

// 从环境变量或配置文件加载配置
function loadConfig() {
  const config = { ...defaultConfig };

  // 从环境变量覆盖配置
  if (process.env.PORTS) {
    try {
      config.ports = JSON.parse(process.env.PORTS);
    } catch (error) {
      console.warn('解析PORTS环境变量失败，使用默认配置');
    }
  }

  if (process.env.RATE_LIMIT_MAX) {
    config.rateLimit.max = parseInt(process.env.RATE_LIMIT_MAX);
  }

  if (process.env.PROXY_TIMEOUT) {
    config.proxy.timeout = parseInt(process.env.PROXY_TIMEOUT);
  }

  return config;
}

module.exports = loadConfig();
