// 调试版本的服务器
const http = require('http');

console.log('开始启动调试服务器...');

// 创建简单的服务器
const server = http.createServer((req, res) => {
  console.log(`收到请求: ${req.method} ${req.url}`);
  
  // 设置响应头
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-TM-Script');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 简单的路由处理
  if (req.url === '/health') {
    const response = {
      success: true,
      data: {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        message: '调试服务器运行正常'
      }
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    return;
  }

  if (req.url === '/test') {
    const response = {
      success: true,
      message: '测试接口正常',
      timestamp: new Date().toISOString()
    };
    res.writeHead(200);
    res.end(JSON.stringify(response, null, 2));
    return;
  }

  // 默认响应
  const response = {
    success: false,
    error: {
      message: '接口不存在',
      path: req.url
    }
  };
  res.writeHead(404);
  res.end(JSON.stringify(response, null, 2));
});

// 监听端口
const PORT = 3000;
server.listen(PORT, () => {
  console.log(`✅ 调试服务器已启动在端口 ${PORT}`);
  console.log(`🔗 测试地址: http://localhost:${PORT}/health`);
  console.log(`🔗 测试地址: http://localhost:${PORT}/test`);
});

server.on('error', (error) => {
  console.error('❌ 服务器错误:', error);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

console.log('调试服务器启动脚本执行完成');
