<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Proxy Service - 测试界面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background: #28a745;
        }
        .status-offline {
            background: #dc3545;
        }
        .status-unknown {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Web Proxy Service 测试界面</h1>
        
        <!-- 服务状态 -->
        <div class="section">
            <h3>📊 服务状态</h3>
            <p>
                <span id="statusIndicator" class="status-indicator status-unknown"></span>
                <span id="statusText">检查中...</span>
            </p>
            <button onclick="checkHealth()">检查健康状态</button>
            <button onclick="getServiceInfo()">获取服务信息</button>
            <button onclick="getSystemStatus()">获取系统状态</button>
            <div id="statusResult" class="result info" style="display: none;"></div>
        </div>

        <!-- 代理测试 -->
        <div class="section">
            <h3>🔄 代理服务测试</h3>
            <label>目标URL:</label>
            <input type="text" id="proxyUrl" value="https://httpbin.org/json" placeholder="输入要代理的URL">
            
            <label>HTTP方法:</label>
            <select id="proxyMethod">
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
            </select>
            
            <label>请求数据 (JSON格式，仅POST/PUT):</label>
            <textarea id="proxyData" placeholder='{"key": "value"}'></textarea>
            
            <button onclick="testProxy()">测试代理请求</button>
            <div id="proxyResult" class="result" style="display: none;"></div>
        </div>

        <!-- 脚本猫测试 -->
        <div class="section">
            <h3>🐒 脚本猫功能测试</h3>
            <label>脚本ID:</label>
            <input type="text" id="scriptId" value="test-script-v1.0" placeholder="脚本标识">
            
            <label>JavaScript代码:</label>
            <textarea id="scriptCode" placeholder="输入JavaScript代码">
// 示例脚本
const result = {
    message: "Hello from TamperMonkey!",
    timestamp: new Date().toISOString(),
    random: Math.random()
};
return result;</textarea>
            
            <button onclick="executeScript()">执行脚本</button>
            <button onclick="testStorage()">测试存储</button>
            <button onclick="testCorsProxy()">测试CORS代理</button>
            <div id="scriptResult" class="result" style="display: none;"></div>
        </div>

        <!-- 实时日志 -->
        <div class="section">
            <h3>📝 操作日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="logArea" class="result info" style="display: block; height: 200px;">
等待操作...
            </div>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:3000';
        const TM_BASE = 'http://localhost:3001';

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logArea = document.getElementById('logArea');
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '日志已清空\n';
        }

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            try {
                log(`发送请求: ${options.method || 'GET'} ${url}`);
                const response = await fetch(url, {
                    mode: 'cors',
                    ...options
                });
                
                const data = await response.json();
                log(`响应状态: ${response.status}`);
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, isSuccess = null) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (isSuccess === null) {
                isSuccess = result.success;
            }
            
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }

        // 更新状态指示器
        function updateStatus(isOnline) {
            const indicator = document.getElementById('statusIndicator');
            const text = document.getElementById('statusText');
            
            if (isOnline) {
                indicator.className = 'status-indicator status-online';
                text.textContent = '服务在线';
            } else {
                indicator.className = 'status-indicator status-offline';
                text.textContent = '服务离线';
            }
        }

        // 健康检查
        async function checkHealth() {
            const result = await makeRequest(`${API_BASE}/health`);
            showResult('statusResult', result);
            updateStatus(result.success);
            
            if (result.success) {
                log('✅ 健康检查通过');
            } else {
                log('❌ 健康检查失败');
            }
        }

        // 获取服务信息
        async function getServiceInfo() {
            const result = await makeRequest(`${API_BASE}/api/info`);
            showResult('statusResult', result);
            
            if (result.success) {
                log('✅ 服务信息获取成功');
            } else {
                log('❌ 服务信息获取失败');
            }
        }

        // 获取系统状态
        async function getSystemStatus() {
            const result = await makeRequest(`${API_BASE}/api/status`);
            showResult('statusResult', result);
            
            if (result.success) {
                log('✅ 系统状态获取成功');
            } else {
                log('❌ 系统状态获取失败');
            }
        }

        // 测试代理
        async function testProxy() {
            const url = document.getElementById('proxyUrl').value;
            const method = document.getElementById('proxyMethod').value;
            const dataText = document.getElementById('proxyData').value;
            
            let data = null;
            if (dataText && (method === 'POST' || method === 'PUT')) {
                try {
                    data = JSON.parse(dataText);
                } catch (e) {
                    log('❌ 请求数据JSON格式错误');
                    return;
                }
            }
            
            const requestBody = { url, method, data };
            
            const result = await makeRequest(`${API_BASE}/proxy/request`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });
            
            showResult('proxyResult', result);
            
            if (result.success) {
                log(`✅ 代理请求成功: ${method} ${url}`);
            } else {
                log(`❌ 代理请求失败: ${method} ${url}`);
            }
        }

        // 执行脚本
        async function executeScript() {
            const scriptId = document.getElementById('scriptId').value;
            const script = document.getElementById('scriptCode').value;
            
            const requestBody = { script, context: { test: true } };
            
            const result = await makeRequest(`${TM_BASE}/tampermonkey/execute`, {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'X-TM-Script': scriptId
                },
                body: JSON.stringify(requestBody)
            });
            
            showResult('scriptResult', result);
            
            if (result.success) {
                log(`✅ 脚本执行成功: ${scriptId}`);
            } else {
                log(`❌ 脚本执行失败: ${scriptId}`);
            }
        }

        // 测试存储
        async function testStorage() {
            const scriptId = document.getElementById('scriptId').value;
            const testKey = 'test_key_' + Date.now();
            const testValue = 'test_value_' + Math.random();
            
            // 设置数据
            const setResult = await makeRequest(`${TM_BASE}/tampermonkey/storage`, {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'X-TM-Script': scriptId
                },
                body: JSON.stringify({
                    action: 'set',
                    key: testKey,
                    value: testValue
                })
            });
            
            if (setResult.success) {
                // 获取数据
                const getResult = await makeRequest(`${TM_BASE}/tampermonkey/storage`, {
                    method: 'POST',
                    headers: { 
                        'Content-Type': 'application/json',
                        'X-TM-Script': scriptId
                    },
                    body: JSON.stringify({
                        action: 'get',
                        key: testKey
                    })
                });
                
                showResult('scriptResult', {
                    set: setResult.data,
                    get: getResult.data
                });
                
                if (getResult.success) {
                    log(`✅ 存储测试成功: ${testKey}`);
                } else {
                    log(`❌ 存储读取失败: ${testKey}`);
                }
            } else {
                showResult('scriptResult', setResult);
                log(`❌ 存储写入失败: ${testKey}`);
            }
        }

        // 测试CORS代理
        async function testCorsProxy() {
            const scriptId = document.getElementById('scriptId').value;
            
            const result = await makeRequest(`${TM_BASE}/tampermonkey/cors-proxy`, {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json',
                    'X-TM-Script': scriptId
                },
                body: JSON.stringify({
                    url: 'https://httpbin.org/user-agent',
                    method: 'GET'
                })
            });
            
            showResult('scriptResult', result);
            
            if (result.success) {
                log('✅ CORS代理测试成功');
            } else {
                log('❌ CORS代理测试失败');
            }
        }

        // 页面加载时自动检查健康状态
        window.onload = function() {
            log('🚀 Web Proxy Service 测试界面已加载');
            log('正在检查服务状态...');
            checkHealth();
        };
    </script>
</body>
</html>
