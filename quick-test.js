// 快速测试脚本
const http = require('http');

console.log('🧪 开始快速测试...');

// 测试健康检查
const options = {
  hostname: '127.0.0.1',
  port: 3000,
  path: '/health',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(`✅ 连接成功! 状态码: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('📋 响应数据:');
      console.log(JSON.stringify(response, null, 2));
      
      if (response.success) {
        console.log('🎉 服务运行正常!');
      } else {
        console.log('⚠️ 服务响应异常');
      }
    } catch (error) {
      console.log('📄 原始响应:', data);
    }
    
    process.exit(0);
  });
});

req.on('error', (error) => {
  console.error('❌ 连接失败:', error.message);
  
  if (error.code === 'ECONNREFUSED') {
    console.log('💡 提示: 服务器可能未启动或端口被占用');
  }
  
  process.exit(1);
});

req.end();

// 超时处理
setTimeout(() => {
  console.log('⏰ 测试超时');
  process.exit(1);
}, 5000);
