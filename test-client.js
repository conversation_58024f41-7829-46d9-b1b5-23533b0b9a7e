#!/usr/bin/env node

/**
 * Web Proxy Service 测试客户端
 */

const http = require('http');

class TestClient {
  constructor(baseUrl = 'localhost', port = 3000) {
    this.baseUrl = baseUrl;
    this.port = port;
  }

  async request(path, method = 'GET', data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const options = {
        hostname: this.baseUrl,
        port: this.port,
        path: path,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };

      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', chunk => body += chunk);
        res.on('end', () => {
          try {
            const result = {
              statusCode: res.statusCode,
              headers: res.headers,
              body: body ? JSON.parse(body) : null
            };
            resolve(result);
          } catch (error) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              body: body
            });
          }
        });
      });

      req.on('error', reject);
      req.setTimeout(10000, () => {
        req.destroy();
        reject(new Error('请求超时'));
      });

      if (data) {
        req.write(JSON.stringify(data));
      }
      req.end();
    });
  }

  async get(path, headers = {}) {
    return this.request(path, 'GET', null, headers);
  }

  async post(path, data, headers = {}) {
    return this.request(path, 'POST', data, headers);
  }
}

// 测试套件
class TestSuite {
  constructor() {
    this.mainClient = new TestClient('localhost', 3000);
    this.tmClient = new TestClient('localhost', 3001);
    this.passed = 0;
    this.failed = 0;
  }

  log(message, data = '') {
    console.log(`${message}${data ? ' ' + JSON.stringify(data, null, 2) : ''}`);
  }

  assert(condition, message) {
    if (condition) {
      this.passed++;
      console.log(`✅ ${message}`);
    } else {
      this.failed++;
      console.log(`❌ ${message}`);
    }
  }

  async testHealthCheck() {
    this.log('\n🔍 测试健康检查...');
    try {
      const response = await this.mainClient.get('/health');
      this.assert(response.statusCode === 200, '健康检查状态码正确');
      this.assert(response.body && response.body.success, '健康检查响应格式正确');
      this.assert(response.body.data && response.body.data.status === 'ok', '服务状态正常');
      this.log('   响应数据:', response.body.data);
    } catch (error) {
      this.assert(false, `健康检查失败: ${error.message}`);
    }
  }

  async testAPIInfo() {
    this.log('\n📖 测试API信息...');
    try {
      const response = await this.mainClient.get('/api/info');
      this.assert(response.statusCode === 200, 'API信息状态码正确');
      this.assert(response.body && response.body.success, 'API信息响应格式正确');
      this.assert(response.body.data && response.body.data.name, '服务名称存在');
      this.log('   服务信息:', {
        name: response.body.data.name,
        version: response.body.data.version,
        features: response.body.data.features
      });
    } catch (error) {
      this.assert(false, `API信息获取失败: ${error.message}`);
    }
  }

  async runAllTests() {
    console.log('🧪 开始运行 Web Proxy Service 测试套件...');
    console.log('='.repeat(50));

    await this.testHealthCheck();
    await this.testAPIInfo();

    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${this.passed}`);
    console.log(`❌ 失败: ${this.failed}`);
    console.log(`📈 成功率: ${((this.passed / (this.passed + this.failed)) * 100).toFixed(1)}%`);

    if (this.failed === 0) {
      console.log('🎉 所有测试通过！服务运行正常。');
    } else {
      console.log('⚠️ 部分测试失败，请检查服务状态。');
    }

    return this.failed === 0;
  }
}

// 运行测试
async function main() {
  const testSuite = new TestSuite();

  try {
    const success = await testSuite.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message);
    console.log('\n💡 提示:');
    console.log('1. 确保服务正在运行: node server.js');
    console.log('2. 检查端口3000和3001是否可用');
    console.log('3. 检查防火墙设置');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { TestClient, TestSuite };
