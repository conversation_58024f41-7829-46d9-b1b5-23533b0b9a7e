const WebSocket = require('ws');
const logger = require('../utils/logger');
const config = require('../config');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map();
    this.rooms = new Map();
  }

  // 启动WebSocket服务器
  start(port = config.tampermonkey.wsPort) {
    this.wss = new WebSocket.Server({ 
      port,
      perMessageDeflate: false
    });

    this.wss.on('connection', (ws, req) => {
      const clientId = require('crypto').randomUUID();
      const clientInfo = {
        id: clientId,
        ip: req.socket.remoteAddress,
        userAgent: req.headers['user-agent'],
        connectedAt: new Date().toISOString()
      };

      this.clients.set(clientId, { ws, info: clientInfo });

      logger.info('WebSocket client connected:', clientInfo);

      // 发送欢迎消息
      this.sendToClient(clientId, {
        type: 'welcome',
        clientId,
        timestamp: new Date().toISOString()
      });

      // 处理消息
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(clientId, message);
        } catch (error) {
          logger.error('WebSocket message parse error:', error);
          this.sendToClient(clientId, {
            type: 'error',
            message: '消息格式错误',
            timestamp: new Date().toISOString()
          });
        }
      });

      // 处理连接关闭
      ws.on('close', (code, reason) => {
        logger.info('WebSocket client disconnected:', {
          clientId,
          code,
          reason: reason.toString(),
          ...clientInfo
        });
        
        this.removeClientFromRooms(clientId);
        this.clients.delete(clientId);
      });

      // 处理错误
      ws.on('error', (error) => {
        logger.error('WebSocket client error:', {
          clientId,
          error: error.message,
          ...clientInfo
        });
      });

      // 心跳检测
      ws.isAlive = true;
      ws.on('pong', () => {
        ws.isAlive = true;
      });
    });

    // 心跳检测定时器
    this.heartbeatInterval = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          return ws.terminate();
        }
        
        ws.isAlive = false;
        ws.ping();
      });
    }, 30000);

    logger.info(`WebSocket server started on port ${port}`);
  }

  // 处理客户端消息
  handleMessage(clientId, message) {
    const { type, data } = message;

    logger.logTampermonkey('websocket_message', {
      clientId,
      type,
      hasData: !!data
    });

    switch (type) {
      case 'join_room':
        this.joinRoom(clientId, data.room);
        break;

      case 'leave_room':
        this.leaveRoom(clientId, data.room);
        break;

      case 'broadcast':
        this.broadcastToRoom(data.room, {
          type: 'broadcast',
          from: clientId,
          data: data.message,
          timestamp: new Date().toISOString()
        });
        break;

      case 'private_message':
        this.sendToClient(data.targetClientId, {
          type: 'private_message',
          from: clientId,
          data: data.message,
          timestamp: new Date().toISOString()
        });
        break;

      case 'script_event':
        this.handleScriptEvent(clientId, data);
        break;

      case 'ping':
        this.sendToClient(clientId, {
          type: 'pong',
          timestamp: new Date().toISOString()
        });
        break;

      default:
        this.sendToClient(clientId, {
          type: 'error',
          message: `未知的消息类型: ${type}`,
          timestamp: new Date().toISOString()
        });
    }
  }

  // 加入房间
  joinRoom(clientId, roomName) {
    if (!this.rooms.has(roomName)) {
      this.rooms.set(roomName, new Set());
    }
    
    this.rooms.get(roomName).add(clientId);
    
    this.sendToClient(clientId, {
      type: 'room_joined',
      room: roomName,
      timestamp: new Date().toISOString()
    });

    // 通知房间内其他客户端
    this.broadcastToRoom(roomName, {
      type: 'user_joined',
      clientId,
      room: roomName,
      timestamp: new Date().toISOString()
    }, clientId);

    logger.logTampermonkey('room_joined', { clientId, room: roomName });
  }

  // 离开房间
  leaveRoom(clientId, roomName) {
    if (this.rooms.has(roomName)) {
      this.rooms.get(roomName).delete(clientId);
      
      if (this.rooms.get(roomName).size === 0) {
        this.rooms.delete(roomName);
      }
    }

    this.sendToClient(clientId, {
      type: 'room_left',
      room: roomName,
      timestamp: new Date().toISOString()
    });

    // 通知房间内其他客户端
    this.broadcastToRoom(roomName, {
      type: 'user_left',
      clientId,
      room: roomName,
      timestamp: new Date().toISOString()
    });

    logger.logTampermonkey('room_left', { clientId, room: roomName });
  }

  // 从所有房间移除客户端
  removeClientFromRooms(clientId) {
    for (const [roomName, clients] of this.rooms) {
      if (clients.has(clientId)) {
        clients.delete(clientId);
        
        // 通知房间内其他客户端
        this.broadcastToRoom(roomName, {
          type: 'user_left',
          clientId,
          room: roomName,
          timestamp: new Date().toISOString()
        });

        if (clients.size === 0) {
          this.rooms.delete(roomName);
        }
      }
    }
  }

  // 处理脚本事件
  handleScriptEvent(clientId, data) {
    const { event, payload, targetRoom } = data;
    
    if (targetRoom) {
      this.broadcastToRoom(targetRoom, {
        type: 'script_event',
        event,
        payload,
        from: clientId,
        timestamp: new Date().toISOString()
      });
    } else {
      // 广播给所有客户端
      this.broadcast({
        type: 'script_event',
        event,
        payload,
        from: clientId,
        timestamp: new Date().toISOString()
      });
    }

    logger.logTampermonkey('script_event', {
      clientId,
      event,
      targetRoom,
      hasPayload: !!payload
    });
  }

  // 发送消息给特定客户端
  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
      return true;
    }
    return false;
  }

  // 广播消息给房间内所有客户端
  broadcastToRoom(roomName, message, excludeClientId = null) {
    const room = this.rooms.get(roomName);
    if (!room) return;

    let sentCount = 0;
    for (const clientId of room) {
      if (clientId !== excludeClientId) {
        if (this.sendToClient(clientId, message)) {
          sentCount++;
        }
      }
    }

    return sentCount;
  }

  // 广播消息给所有客户端
  broadcast(message, excludeClientId = null) {
    let sentCount = 0;
    for (const [clientId] of this.clients) {
      if (clientId !== excludeClientId) {
        if (this.sendToClient(clientId, message)) {
          sentCount++;
        }
      }
    }
    return sentCount;
  }

  // 获取统计信息
  getStats() {
    return {
      connectedClients: this.clients.size,
      activeRooms: this.rooms.size,
      roomDetails: Array.from(this.rooms.entries()).map(([name, clients]) => ({
        name,
        clientCount: clients.size
      }))
    };
  }

  // 关闭WebSocket服务器
  close() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    if (this.wss) {
      this.wss.close();
      logger.info('WebSocket server closed');
    }
  }
}

module.exports = WebSocketService;
